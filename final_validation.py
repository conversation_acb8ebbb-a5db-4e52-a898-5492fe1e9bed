#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证脚本 - 测试原始失败的用例
"""

import os
import sys
from weather_visualization import WeatherDataVisualizer

def test_original_failing_case():
    """测试原始失败的用例"""
    print("=" * 60)
    print("最终验证：测试原始失败的用例")
    print("=" * 60)
    
    print("测试场景：调用 plot_comprehensive_analysis() 方法")
    print("原始错误：ValueError: to assemble mappings requires at least that [year, month, day] be specified")
    print()
    
    # 创建可视化器
    visualizer = WeatherDataVisualizer(output_dir='./final_validation_charts')
    
    # 加载日度数据
    daily_file = '丽水市_完整天气数据_20250725_100235.csv'
    if not os.path.exists(daily_file):
        print(f"❌ 数据文件不存在: {daily_file}")
        return False
    
    print(f"1. 加载数据文件: {daily_file}")
    success = visualizer.load_daily_data(daily_file)
    if not success:
        print("❌ 数据加载失败")
        return False
    
    print(f"✅ 数据加载成功")
    print(f"   - 记录数量: {len(visualizer.daily_data)}")
    print(f"   - 时间范围: {visualizer.daily_data['日期'].min()} 到 {visualizer.daily_data['日期'].max()}")
    print(f"   - 年份字段: {visualizer.daily_data['年'].min()} - {visualizer.daily_data['年'].max()}")
    print(f"   - 月份字段: {visualizer.daily_data['月'].min()} - {visualizer.daily_data['月'].max()}")
    
    # 测试原始失败的方法
    print(f"\n2. 测试 plot_comprehensive_analysis() 方法")
    try:
        result = visualizer.plot_comprehensive_analysis()
        if result and os.path.exists(result):
            print(f"✅ plot_comprehensive_analysis() 成功执行")
            print(f"   生成文件: {result}")
            print(f"   文件大小: {os.path.getsize(result)} 字节")
            return True
        else:
            print(f"❌ plot_comprehensive_analysis() 执行失败：未生成文件")
            return False
    except Exception as e:
        print(f"❌ plot_comprehensive_analysis() 执行异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_datetime_conversion_directly():
    """直接测试日期时间转换"""
    print(f"\n3. 直接测试日期时间转换逻辑")
    
    try:
        import pandas as pd
        
        # 模拟原始问题的数据
        test_data = pd.DataFrame({
            '年': [2019, 2020, 2021],
            '月': [1, 6, 12],
            '白天天气': ['晴', '阴', '雨'],
            '夜间天气': ['晴', '阴', '雨']
        })
        
        print("   测试数据:")
        print(test_data)
        
        # 测试原始的失败方法
        print("\n   测试原始方法 (应该失败):")
        try:
            # 这是原始的失败代码
            result_old = pd.to_datetime(test_data[['年', '月']].assign(日=1))
            print(f"   ⚠️  原始方法意外成功: {result_old}")
        except Exception as e:
            print(f"   ✅ 原始方法确实失败: {e}")
        
        # 测试修复后的方法
        print("\n   测试修复后的方法:")
        try:
            # 这是修复后的代码
            result_new = pd.to_datetime(
                test_data['年'].astype(str) + '-' + 
                test_data['月'].astype(str).str.zfill(2) + '-01',
                format='%Y-%m-%d'
            )
            print(f"   ✅ 修复后的方法成功: {result_new.tolist()}")
            return True
        except Exception as e:
            print(f"   ❌ 修复后的方法失败: {e}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")
        return False

def test_edge_cases():
    """测试边界情况"""
    print(f"\n4. 测试边界情况")
    
    try:
        import pandas as pd
        
        # 测试各种边界情况
        edge_cases = [
            ("单月数据", pd.DataFrame({'年': [2023], '月': [1]})),
            ("跨年数据", pd.DataFrame({'年': [2022, 2023], '月': [12, 1]})),
            ("所有月份", pd.DataFrame({'年': [2023] * 12, '月': list(range(1, 13))})),
        ]
        
        for case_name, test_data in edge_cases:
            print(f"   测试 {case_name}:")
            try:
                result = pd.to_datetime(
                    test_data['年'].astype(str) + '-' + 
                    test_data['月'].astype(str).str.zfill(2) + '-01',
                    format='%Y-%m-%d'
                )
                print(f"   ✅ {case_name} 成功: {len(result)} 个日期")
            except Exception as e:
                print(f"   ❌ {case_name} 失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 边界测试异常: {e}")
        return False

def main():
    """主函数"""
    print("天气数据可视化系统 - 最终验证")
    print("验证 pandas 日期时间转换问题的修复")
    
    # 运行所有验证测试
    test_results = []
    
    test_results.append(("原始失败用例", test_original_failing_case()))
    test_results.append(("日期转换逻辑", test_datetime_conversion_directly()))
    test_results.append(("边界情况测试", test_edge_cases()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("最终验证结果")
    print("=" * 60)
    
    passed_tests = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n验证结果: {passed_tests}/{len(test_results)} 项测试通过")
    
    if passed_tests == len(test_results):
        print("\n🎉 最终验证成功！")
        print("✅ pandas 日期时间转换问题已完全解决")
        print("✅ plot_comprehensive_analysis() 方法正常工作")
        print("✅ 所有边界情况都能正确处理")
        print("✅ 天气数据可视化系统完全可用")
        
        print("\n📊 修复总结:")
        print("问题: ValueError: to assemble mappings requires at least that [year, month, day] be specified")
        print("原因: pandas.to_datetime() 对列名映射的处理在某些版本中存在问题")
        print("解决方案: 使用字符串格式化方法代替列名映射")
        print("修复代码:")
        print("  # 原始代码 (失败)")
        print("  df['日期'] = pd.to_datetime(df[['年', '月']].assign(日=1))")
        print("  # 修复代码 (成功)")
        print("  df['日期'] = pd.to_datetime(")
        print("      df['年'].astype(str) + '-' + ")
        print("      df['月'].astype(str).str.zfill(2) + '-01',")
        print("      format='%Y-%m-%d'")
        print("  )")
        
    else:
        print(f"\n❌ 验证失败: {len(test_results) - passed_tests} 项测试未通过")
        print("需要进一步调试")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
