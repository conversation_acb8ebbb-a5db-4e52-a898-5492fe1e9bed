# 天气数据可视化分析工具

## 项目简介

这是一个专门用于天气数据可视化分析的Python工具包，支持对日度和月度天气数据进行多维度的图表分析和统计报告生成。

## 功能特点

- ✅ 支持日度和月度天气数据分析
- ✅ 多种可视化图表类型
- ✅ 自动生成分析报告
- ✅ 中文字体支持
- ✅ 高质量图片输出（300DPI）
- ✅ 完善的错误处理机制

## 支持的图表类型

### 1. 温度趋势图
- **日度数据**：显示每日最高/最低温度变化趋势，包含30日移动平均线
- **月度数据**：显示月平均最高/最低温度变化趋势

### 2. 天气状况分布图
- **日度数据**：白天和夜间天气状况的饼图分布
- **月度数据**：主要天气状况的分布统计

### 3. 季节性分析图
- 各季节温度分布箱线图
- 月度平均温度变化曲线
- 年度平均温度变化趋势

### 4. 综合分析图
- 温度分布直方图
- 最高温度vs最低温度散点图
- 月度天气天数统计
- 年度极值温度变化

### 5. 数据摘要报告
- 基本统计信息
- 季节温度统计
- 主要天气状况统计

## 环境要求

### Python版本
- Python 3.6 或更高版本

### 依赖库
```bash
pip install pandas matplotlib numpy
```

## 文件结构

```
天气可视化工具/
├── weather_visualization.py    # 主要可视化类
├── test_visualization.py       # 基本功能测试脚本
├── demo_visualization.py       # 完整演示脚本
├── README_可视化工具.md        # 本说明文档
├── test_charts/                # 测试图表输出目录
├── demo_charts/                # 演示图表输出目录
└── comprehensive_charts/       # 综合分析图表输出目录
```

## 使用方法

### 1. 基本使用

```python
from weather_visualization import WeatherDataVisualizer

# 创建可视化分析器
visualizer = WeatherDataVisualizer(output_dir='./charts')

# 加载日度数据
visualizer.load_daily_data('丽水市_完整天气数据_20250725_100235.csv')

# 生成温度趋势图
visualizer.plot_temperature_trend('daily')

# 生成天气分布图
visualizer.plot_weather_distribution('daily')

# 生成季节性分析图
visualizer.plot_seasonal_analysis('daily')

# 生成综合分析图
visualizer.plot_comprehensive_analysis()

# 生成分析报告
visualizer.generate_summary_report()
```

### 2. 月度数据分析

```python
# 加载月度数据
visualizer.load_monthly_data('weather_data_new/丽水市_月度统计_201901_202507.csv')

# 生成月度图表
visualizer.plot_temperature_trend('monthly')
visualizer.plot_weather_distribution('monthly')
visualizer.plot_seasonal_analysis('monthly')
```

### 3. 一键生成所有图表

```python
# 一键生成所有可视化图表和报告
visualizer.create_all_visualizations(
    daily_file='丽水市_完整天气数据_20250725_100235.csv',
    monthly_file='weather_data_new/丽水市_月度统计_201901_202507.csv'
)
```

### 4. 快速测试

```bash
# 运行基本功能测试
python test_visualization.py

# 运行完整演示
python demo_visualization.py

# 运行主程序（使用默认文件）
python weather_visualization.py
```

## 数据格式要求

### 日度数据格式
CSV文件必须包含以下字段：
- **日期**：格式为 "YYYY年MM月DD日"
- **天气状况**：格式为 "白天天气 / 夜间天气"
- **气温**：格式为 "最高温℃ / 最低温℃"
- **风力风向**：风向和风力信息

示例：
```csv
日期,天气状况,气温,风力风向
2019年01月02日,阵雨 / 阴,6℃ / 3℃,无持续风向 1-2级 / 无持续风向 1-2级
```

### 月度数据格式
CSV文件必须包含以下字段：
- **年**、**月**：数值格式
- **平均最高温**、**平均最低温**：数值格式
- 其他统计字段（可选）

示例：
```csv
年,月,平均最高温,平均最低温,主要白天天气,主要夜间天气
2019,1,12.6,4.5,晴,晴
```

## 输出文件说明

### 图表文件
- **格式**：PNG图片，300DPI高分辨率
- **命名**：城市名_图表类型.png
- **特点**：支持中文显示，包含完整的标题、坐标轴标签和图例

### 分析报告
- **格式**：UTF-8编码的文本文件
- **内容**：基本统计、季节分析、天气状况统计
- **命名**：城市名_天气分析报告.txt

## 注意事项

1. **中文字体**：脚本会自动尝试使用系统中的中文字体，如遇显示问题请安装SimHei或Microsoft YaHei字体

2. **数据质量**：脚本会自动过滤无效数据，但建议使用高质量的数据源

3. **内存使用**：处理大量数据时注意内存使用，建议单次处理不超过10万条记录

4. **图表显示**：在某些环境中可能无法直接显示图表，但图片文件会正常保存

## 错误处理

脚本包含完善的错误处理机制：
- 自动检测文件存在性
- 验证数据格式
- 处理缺失数据
- 提供详细的错误信息

## 扩展功能

可以通过继承`WeatherDataVisualizer`类来添加自定义分析功能：

```python
class CustomAnalyzer(WeatherDataVisualizer):
    def custom_analysis(self):
        # 添加自定义分析逻辑
        pass
```

## 技术支持

如遇问题，请检查：
1. Python版本和依赖库是否正确安装
2. 数据文件格式是否符合要求
3. 输出目录是否有写入权限
4. 系统是否支持中文字体显示
