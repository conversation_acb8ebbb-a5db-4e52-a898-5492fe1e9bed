# 天气数据处理系统说明

本系统提供了完整的天气数据处理流程，可以将爬取的日度天气数据转换为月度统计数据，并进行深度分析和可视化。

## 📁 文件结构

```
天气数据处理系统/
├── weather_data_processor.py    # 日度→月度数据转换器
├── weather_analysis.py          # 月度数据分析器
├── 使用示例.py                   # 完整使用示例
├── 数据处理说明.md               # 本说明文档
└── weather_data_new/            # 原始数据目录
    └── 丽水市_201901_202507.csv  # 示例：丽水市日度数据
```

## 🔧 功能模块

### 1. WeatherDataProcessor (数据转换器)

**功能**: 将日度天气数据转换为月度统计数据

**输入**: 日度天气CSV文件，包含以下字段：
- 日期 (如: 2023年01月01日)
- 天气状况 (如: 晴 / 多云)
- 气温 (如: 14℃ / 3℃)
- 风力风向 (如: 东风 1-3级 / 西风 1-3级)

**输出**: 月度统计CSV文件，包含以下字段：
- 年、月、年月
- 天数
- 月最高温、月最低温
- 平均最高温、平均最低温、平均温度
- 主要白天天气、主要夜间天气
- 降水天数、晴天天数、多云天数

**使用方法**:
```python
from weather_data_processor import WeatherDataProcessor

processor = WeatherDataProcessor()
processor.load_data("丽水市_201901_202507.csv")
processor.process_to_monthly()
processor.save_monthly_stats("丽水市_月度统计.csv")
processor.print_summary()
```

### 2. WeatherAnalyzer (数据分析器)

**功能**: 对月度统计数据进行深度分析

**分析内容**:
- 基础统计分析 (温度、降水、天气状况统计)
- 季节性分析 (春夏秋冬各季节特征)
- 年度趋势分析 (多年变化趋势)
- 极端天气分析 (异常高温、低温、降水)

**可视化图表**:
- 温度降水趋势图
- 季节性分析图
- 年度统计图表
- 温度热力图

**使用方法**:
```python
from weather_analysis import WeatherAnalyzer

analyzer = WeatherAnalyzer("丽水市_月度统计.csv")
analyzer.load_data()
analyzer.basic_statistics()
analyzer.seasonal_analysis()
analyzer.create_visualizations()
analyzer.generate_report()
```

## 📊 处理结果示例

### 丽水市天气数据分析结果 (2019-2025)

**基础统计**:
- 历史最高温: 41℃ (2022年7月)
- 历史最低温: -7℃ (2024年1月)
- 平均温度: 19.7℃
- 月平均降水天数: 15.7天

**季节特征**:
- 冬季: 平均9.3℃, 降水12.4天
- 春季: 平均19.2℃, 降水17.6天
- 夏季: 平均29.0℃, 降水19.8天
- 秋季: 平均21.2℃, 降水12.6天

**年度趋势**:
- 温度趋势: 每年下降0.044℃
- 降水变化: 相对稳定

## 🚀 快速开始

### 方法1: 处理单个城市数据

```python
# 直接运行数据处理器
python weather_data_processor.py

# 直接运行数据分析器
python weather_analysis.py
```

### 方法2: 使用完整流程

```python
# 运行使用示例，包含完整处理流程
python 使用示例.py
```

### 方法3: 自定义处理

```python
from weather_data_processor import WeatherDataProcessor
from weather_analysis import WeatherAnalyzer

# 步骤1: 转换数据
processor = WeatherDataProcessor()
processor.load_data("你的数据文件.csv")
processor.process_to_monthly()
processor.save_monthly_stats("月度统计.csv")

# 步骤2: 分析数据
analyzer = WeatherAnalyzer("月度统计.csv")
analyzer.load_data()
analyzer.basic_statistics()
analyzer.create_visualizations()
```

## 📈 输出文件说明

### 1. 月度统计CSV文件
包含每个月的详细统计数据，可用于进一步分析或导入其他工具。

### 2. 可视化图表
- `城市名_温度降水趋势.png`: 时间序列趋势图
- `城市名_季节性分析.png`: 季节性特征分析图

### 3. 分析报告
- `城市名_天气分析报告.txt`: 文本格式的分析摘要

## ⚙️ 配置选项

### 数据处理器配置
- `output_dir`: 输出目录路径
- 编码自动检测: 支持UTF-8、GBK等多种编码

### 分析器配置
- `output_dir`: 图表输出目录
- 图表样式: 可自定义matplotlib样式
- 报告格式: 支持自定义报告模板

## 🔍 数据质量检查

系统内置多层数据验证：

1. **日期解析验证**: 自动识别和解析各种日期格式
2. **温度数据验证**: 检查温度数据的合理性
3. **天气状况标准化**: 统一天气描述格式
4. **缺失数据处理**: 自动跳过无效数据并记录

## 📋 使用注意事项

1. **数据格式要求**: 
   - CSV文件必须包含：日期、天气状况、气温、风力风向四列
   - 日期格式应为：YYYY年MM月DD日

2. **文件编码**: 
   - 支持UTF-8、GBK等编码
   - 建议使用UTF-8编码以避免中文显示问题

3. **内存使用**: 
   - 大文件处理时注意内存使用
   - 建议单个文件不超过100MB

4. **图表显示**: 
   - 如遇中文字体问题，请安装相应中文字体
   - 图表会自动保存为PNG格式

## 🛠️ 扩展功能

### 批量处理
```python
# 批量处理多个城市数据
from 使用示例 import batch_process_multiple_cities
batch_process_multiple_cities("数据目录", "输出目录")
```

### 多城市对比
```python
# 生成多城市对比报告
from 使用示例 import generate_comparison_report
generate_comparison_report("处理结果目录")
```

### 自定义分析
可以继承`WeatherAnalyzer`类，添加自定义分析方法：

```python
class CustomAnalyzer(WeatherAnalyzer):
    def custom_analysis(self):
        # 添加你的自定义分析逻辑
        pass
```

## 📞 技术支持

如遇到问题，请检查：
1. 数据文件格式是否正确
2. 文件路径是否存在
3. 是否有足够的磁盘空间
4. Python环境是否安装了所需依赖包

依赖包列表：
- pandas
- numpy
- matplotlib
- seaborn
- logging (内置)
- os (内置)
- re (内置)
