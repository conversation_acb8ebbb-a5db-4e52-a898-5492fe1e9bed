#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气数据可视化演示脚本
演示如何使用WeatherDataVisualizer类进行天气数据可视化分析
"""

from weather_visualization import WeatherDataVisualizer
import os

def demo_daily_data_analysis():
    """演示日度数据分析"""
    print("=" * 60)
    print("演示：日度天气数据可视化分析")
    print("=" * 60)
    
    # 创建可视化分析器
    visualizer = WeatherDataVisualizer(output_dir='./demo_charts')
    
    # 日度数据文件
    daily_file = "丽水市_完整天气数据_20250725_100235.csv"
    
    if not os.path.exists(daily_file):
        print(f"错误：找不到日度数据文件 {daily_file}")
        return
    
    # 加载日度数据
    if visualizer.load_daily_data(daily_file):
        print("\n正在生成日度数据可视化图表...")
        
        # 生成各种图表
        try:
            # 1. 温度趋势图
            print("1. 生成温度趋势图...")
            visualizer.plot_temperature_trend('daily')
            
            # 2. 天气分布图
            print("2. 生成天气分布图...")
            visualizer.plot_weather_distribution('daily')
            
            # 3. 季节性分析图
            print("3. 生成季节性分析图...")
            visualizer.plot_seasonal_analysis('daily')
            
            # 4. 综合分析图
            print("4. 生成综合分析图...")
            visualizer.plot_comprehensive_analysis()
            
            # 5. 生成分析报告
            print("5. 生成分析报告...")
            visualizer.generate_summary_report()
            
            print("\n日度数据分析完成！")
            
        except Exception as e:
            print(f"生成图表时出现错误: {e}")
            import traceback
            traceback.print_exc()

def demo_monthly_data_analysis():
    """演示月度数据分析"""
    print("\n" + "=" * 60)
    print("演示：月度天气数据可视化分析")
    print("=" * 60)
    
    # 创建可视化分析器
    visualizer = WeatherDataVisualizer(output_dir='./demo_charts')
    
    # 月度数据文件
    monthly_file = "weather_data_new/丽水市_月度统计_201901_202507.csv"
    
    if not os.path.exists(monthly_file):
        print(f"错误：找不到月度数据文件 {monthly_file}")
        return
    
    # 加载月度数据
    if visualizer.load_monthly_data(monthly_file):
        print("\n正在生成月度数据可视化图表...")
        
        try:
            # 1. 月度温度趋势图
            print("1. 生成月度温度趋势图...")
            visualizer.plot_temperature_trend('monthly')
            
            # 2. 月度天气分布图
            print("2. 生成月度天气分布图...")
            visualizer.plot_weather_distribution('monthly')
            
            # 3. 月度季节性分析图
            print("3. 生成月度季节性分析图...")
            visualizer.plot_seasonal_analysis('monthly')
            
            print("\n月度数据分析完成！")
            
        except Exception as e:
            print(f"生成图表时出现错误: {e}")
            import traceback
            traceback.print_exc()

def demo_comprehensive_analysis():
    """演示综合分析（一键生成所有图表）"""
    print("\n" + "=" * 60)
    print("演示：一键生成所有可视化图表")
    print("=" * 60)
    
    # 创建可视化分析器
    visualizer = WeatherDataVisualizer(output_dir='./comprehensive_charts')
    
    # 数据文件路径
    daily_file = "丽水市_完整天气数据_20250725_100235.csv"
    monthly_file = "weather_data_new/丽水市_月度统计_201901_202507.csv"
    
    # 检查文件存在性
    daily_exists = os.path.exists(daily_file)
    monthly_exists = os.path.exists(monthly_file)
    
    if not daily_exists:
        print(f"警告：日度数据文件不存在: {daily_file}")
        daily_file = None
    
    if not monthly_exists:
        print(f"警告：月度数据文件不存在: {monthly_file}")
        monthly_file = None
    
    if not daily_exists and not monthly_exists:
        print("错误：没有找到任何可用的数据文件")
        return
    
    # 一键生成所有图表
    try:
        visualizer.create_all_visualizations(daily_file, monthly_file)
    except Exception as e:
        print(f"生成图表时出现错误: {e}")
        import traceback
        traceback.print_exc()

def show_usage_examples():
    """显示使用示例"""
    print("\n" + "=" * 60)
    print("天气数据可视化工具使用示例")
    print("=" * 60)
    
    print("""
1. 基本使用方法：
   from weather_visualization import WeatherDataVisualizer
   
   # 创建可视化分析器
   visualizer = WeatherDataVisualizer(output_dir='./charts')
   
   # 加载日度数据并生成图表
   visualizer.load_daily_data('日度数据文件.csv')
   visualizer.plot_temperature_trend('daily')
   visualizer.plot_weather_distribution('daily')
   
2. 支持的图表类型：
   - 温度趋势图（时间序列）
   - 天气状况分布图（饼图）
   - 季节性温度分析（箱线图、折线图）
   - 综合分析图（直方图、散点图、统计图）
   - 数据摘要报告（文本文件）
   
3. 数据格式要求：
   - 日度数据：包含日期、天气状况、气温、风力风向字段
   - 月度数据：包含年、月、平均最高温、平均最低温等字段
   - 日期格式：YYYY年MM月DD日
   - 温度格式：最高℃ / 最低℃
   
4. 输出文件：
   - 所有图表保存为PNG格式，分辨率300DPI
   - 分析报告保存为UTF-8编码的文本文件
   - 支持中文字体显示
""")

def main():
    """主函数"""
    print("天气数据可视化演示程序")
    print("=" * 60)
    
    # 显示使用示例
    show_usage_examples()
    
    # 演示日度数据分析
    demo_daily_data_analysis()
    
    # 演示月度数据分析
    demo_monthly_data_analysis()
    
    # 演示综合分析
    demo_comprehensive_analysis()
    
    print("\n" + "=" * 60)
    print("所有演示完成！")
    print("生成的图表文件保存在以下目录：")
    print("- ./demo_charts/ (单独功能演示)")
    print("- ./comprehensive_charts/ (综合分析)")
    print("=" * 60)

if __name__ == "__main__":
    main()
