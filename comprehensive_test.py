#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合测试脚本 - 验证所有功能正常工作
"""

import os
import sys
from weather_visualization import WeatherDataVisualizer

def test_all_chart_types():
    """测试所有图表类型"""
    print("=" * 60)
    print("综合测试：所有图表类型")
    print("=" * 60)
    
    # 创建可视化器
    visualizer = WeatherDataVisualizer(output_dir='./comprehensive_test_charts')
    
    # 加载日度数据
    daily_file = '丽水市_完整天气数据_20250725_100235.csv'
    if not os.path.exists(daily_file):
        print(f"❌ 日度数据文件不存在: {daily_file}")
        return False
    
    print(f"1. 加载日度数据: {daily_file}")
    success = visualizer.load_daily_data(daily_file)
    if not success:
        print("❌ 日度数据加载失败")
        return False
    print(f"✅ 日度数据加载成功 ({len(visualizer.daily_data)} 条记录)")
    
    # 尝试加载月度数据
    monthly_file = 'weather_data_new/丽水市_月度统计_201901_202507.csv'
    if os.path.exists(monthly_file):
        print(f"\n2. 加载月度数据: {monthly_file}")
        monthly_success = visualizer.load_monthly_data(monthly_file)
        if monthly_success:
            print(f"✅ 月度数据加载成功 ({len(visualizer.monthly_data)} 条记录)")
        else:
            print("⚠️  月度数据加载失败，但不影响主要功能")
    else:
        print("\n⚠️  月度数据文件不存在，跳过月度数据测试")
        monthly_success = False
    
    # 测试所有图表类型
    print(f"\n3. 测试所有图表类型")
    
    test_cases = [
        # 日度数据图表
        ("日度温度趋势图", lambda: visualizer.plot_temperature_trend('daily')),
        ("日度天气分布图", lambda: visualizer.plot_weather_distribution('daily')),
        ("日度季节性分析图", lambda: visualizer.plot_seasonal_analysis('daily')),
        ("综合分析图", lambda: visualizer.plot_comprehensive_analysis()),
        ("数据摘要报告", lambda: visualizer.generate_summary_report()),
    ]
    
    # 如果月度数据加载成功，添加月度图表测试
    if monthly_success:
        test_cases.extend([
            ("月度温度趋势图", lambda: visualizer.plot_temperature_trend('monthly')),
            ("月度天气分布图", lambda: visualizer.plot_weather_distribution('monthly')),
            ("月度季节性分析图", lambda: visualizer.plot_seasonal_analysis('monthly')),
        ])
    
    success_count = 0
    failed_tests = []
    
    for name, func in test_cases:
        try:
            print(f"   测试 {name}...")
            result = func()
            if result and os.path.exists(result):
                print(f"   ✅ {name} 成功: {os.path.basename(result)}")
                success_count += 1
            else:
                print(f"   ❌ {name} 失败: 文件未生成")
                failed_tests.append(name)
        except Exception as e:
            print(f"   ❌ {name} 异常: {e}")
            failed_tests.append(name)
            import traceback
            traceback.print_exc()
    
    print(f"\n4. 测试结果汇总")
    print(f"   成功: {success_count}/{len(test_cases)}")
    print(f"   失败: {len(failed_tests)}")
    
    if failed_tests:
        print(f"   失败的测试: {', '.join(failed_tests)}")
    
    return len(failed_tests) == 0

def test_batch_generation():
    """测试批量生成功能"""
    print(f"\n5. 测试批量生成功能")
    
    try:
        visualizer = WeatherDataVisualizer(output_dir='./batch_test_charts')
        
        # 加载数据
        daily_file = '丽水市_完整天气数据_20250725_100235.csv'
        if os.path.exists(daily_file):
            visualizer.load_daily_data(daily_file)
            
            # 测试 generate_all_charts 方法
            print("   测试 generate_all_charts 方法...")
            files = visualizer.generate_all_charts()
            
            print(f"   批量生成结果: {len(files)} 个文件")
            for file in files:
                if os.path.exists(file):
                    print(f"   ✅ {os.path.basename(file)}")
                else:
                    print(f"   ❌ {os.path.basename(file)} (文件不存在)")
            
            # 测试 create_all_visualizations 方法
            print("   测试 create_all_visualizations 方法...")
            monthly_file = 'weather_data_new/丽水市_月度统计_201901_202507.csv'
            if os.path.exists(monthly_file):
                visualizer.create_all_visualizations(daily_file, monthly_file)
            else:
                visualizer.create_all_visualizations(daily_file)
            
            return len(files) > 0
        else:
            print("   ⚠️  数据文件不存在，跳过批量测试")
            return True
            
    except Exception as e:
        print(f"   ❌ 批量生成异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理"""
    print(f"\n6. 测试错误处理")
    
    visualizer = WeatherDataVisualizer(output_dir='./error_test_charts')
    
    # 测试无数据情况
    try:
        result = visualizer.plot_temperature_trend('daily')
        if not result:
            print("   ✅ 无数据时正确返回空结果")
        else:
            print("   ⚠️  无数据时仍生成了图表")
    except Exception as e:
        print(f"   ❌ 无数据时抛出异常: {e}")
        return False
    
    # 测试错误文件路径
    try:
        success = visualizer.load_daily_data('不存在的文件.csv')
        if not success:
            print("   ✅ 错误文件路径正确处理")
        else:
            print("   ❌ 错误文件路径未正确处理")
            return False
    except Exception as e:
        print(f"   ❌ 错误文件路径抛出异常: {e}")
        return False
    
    return True

def check_output_files():
    """检查输出文件"""
    print(f"\n7. 检查输出文件")
    
    directories = [
        './comprehensive_test_charts',
        './batch_test_charts',
        './error_test_charts'
    ]
    
    total_files = 0
    for directory in directories:
        if os.path.exists(directory):
            files = [f for f in os.listdir(directory) if f.endswith(('.png', '.txt'))]
            if files:
                print(f"   📁 {directory}: {len(files)} 个文件")
                total_files += len(files)
                for file in files[:3]:  # 只显示前3个文件
                    print(f"      - {file}")
                if len(files) > 3:
                    print(f"      ... 还有 {len(files) - 3} 个文件")
    
    print(f"   总计生成文件: {total_files} 个")
    return total_files > 0

def main():
    """主函数"""
    print("天气数据可视化系统 - 综合测试")
    
    # 运行所有测试
    test_results = []
    
    test_results.append(("图表类型测试", test_all_chart_types()))
    test_results.append(("批量生成测试", test_batch_generation()))
    test_results.append(("错误处理测试", test_error_handling()))
    test_results.append(("输出文件检查", check_output_files()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("综合测试结果汇总")
    print("=" * 60)
    
    passed_tests = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n总体结果: {passed_tests}/{len(test_results)} 项测试通过")
    
    if passed_tests == len(test_results):
        print("\n🎉 所有测试通过！")
        print("✅ pandas 日期时间转换问题已完全修复")
        print("✅ 所有可视化功能正常工作")
        print("✅ 错误处理机制完善")
        print("✅ 批量生成功能正常")
        print("\n天气数据可视化系统已准备就绪！")
    else:
        print(f"\n⚠️  {len(test_results) - passed_tests} 项测试失败")
        print("需要进一步调试和修复")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
