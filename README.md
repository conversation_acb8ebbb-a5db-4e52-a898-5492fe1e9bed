# 浙江省天气爬虫 - 增强版

这是一个用于从 tianqihoubao.com 网站爬取历史天气数据的Python爬虫工具，支持多进程并发爬取，具有高度的可配置性和强大的错误处理能力。

## 功能特性

- ✅ **多进程并发**: 支持多进程并发爬取，提高数据收集效率
- ✅ **可配置参数**: 支持自定义城市、日期范围等参数
- ✅ **智能重试**: 内置重试机制，处理网络异常和临时错误
- ✅ **数据验证**: 自动验证爬取的数据完整性
- ✅ **UTF-8编码**: 解决中文编码问题，支持UTF-8格式输出
- ✅ **灵活的日期范围**: 支持单月、多月、跨年等各种日期范围
- ✅ **详细日志**: 提供详细的爬取进度和错误信息
- ✅ **向后兼容**: 保留原有功能，支持旧版本使用方式

## 安装依赖

```bash
pip install pandas numpy requests lxml
```

## 快速开始

### 基本使用

```python
from 浙江天气爬虫_多进程 import WeatherScraper

# 创建爬虫实例
scraper = WeatherScraper(output_dir='./weather_data')

# 配置要爬取的城市
cities = {
    '杭州市': 'hangzhou',
    '宁波市': 'ningbo',
    '温州市': 'wenzhou'
}

# 生成日期范围 (2023年1月到2024年12月)
date_list = scraper.generate_date_range(2023, 1, 2024, 12)

# 开始爬取
results = scraper.scrape_multiple_cities(
    cities=cities,
    date_list=date_list,
    max_workers=4  # 使用4个进程
)

# 查看结果
for city, filepath in results.items():
    if filepath:
        print(f"✓ {city}: {filepath}")
    else:
        print(f"✗ {city}: 爬取失败")
```

### 单个城市爬取

```python
# 爬取单个城市的数据
filepath = scraper.scrape_city_data(
    city_cn='杭州市',
    city_en='hangzhou', 
    date_list=['202301', '202302', '202303']
)
```

### 单月数据获取

```python
# 获取特定月份的数据
weather_data = scraper.fetch_month_data('hangzhou', '202501')
for record in weather_data:
    print(record)
```

## 支持的城市

默认支持以下浙江省城市：

| 中文名 | 英文标识 |
|--------|----------|
| 杭州市 | hangzhou |
| 宁波市 | ningbo |
| 温州市 | wenzhou |
| 嘉兴市 | jiaxing |
| 湖州市 | huzhou |
| 绍兴市 | shaoxing |
| 金华市 | jinhua |
| 衢州市 | quzhou |
| 舟山市 | zhoushan |
| 台州市 | taizhou |
| 丽水市 | lishui |

## 数据格式

爬取的数据保存为CSV格式，包含以下字段：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 日期 | 具体日期 | 2023年01月01日 |
| 天气状况 | 白天/夜间天气 | 晴 / 多云 |
| 气温 | 最高/最低温度 | 14℃ / 3℃ |
| 风力风向 | 白天/夜间风力风向 | 东风 1-3级 / 西风 1-3级 |

## 配置选项

### WeatherScraper 类参数

- `output_dir`: 输出目录，默认为 `'./weather_data'`

### scrape_multiple_cities 方法参数

- `cities`: 城市字典，格式为 `{中文名: 英文标识}`
- `date_list`: 日期列表，格式为 `['YYYYMM', ...]`
- `max_workers`: 最大进程数，默认为 4

### fetch_month_data 方法参数

- `city_en`: 城市英文标识
- `year_month`: 年月，格式为 `'YYYYMM'`
- `max_retries`: 最大重试次数，默认为 3

## 错误处理

爬虫内置了多层错误处理机制：

1. **网络错误**: 自动重试，递增延迟
2. **编码错误**: 尝试多种编码方式 (UTF-8, GBK)
3. **数据解析错误**: 跳过无效数据，记录错误日志
4. **文件保存错误**: 详细的错误信息和建议

## 日志系统

爬虫提供详细的日志信息：

```
2025-07-24 13:56:06,122 - INFO - 开始爬取天气数据...
2025-07-24 13:56:06,122 - INFO - 城市: ['杭州市', '宁波市', '温州市']
2025-07-24 13:56:06,122 - INFO - 日期范围: 202301 到 202412
2025-07-24 13:56:07,636 - INFO - 成功获取 30 条记录
2025-07-24 13:56:33,644 - INFO - 成功保存 杭州市 数据到 ./weather_data_new\杭州市_202301_202412.csv，共 706 条记录
```

## 测试脚本

运行测试脚本验证功能：

```bash
python test_scraper.py
```

测试脚本包含：
- 单个城市单个月份测试
- 单个城市多个月份测试  
- 多个城市并发测试
- 自定义日期范围测试

## 注意事项

1. **请求频率**: 内置了请求延迟，避免对服务器造成过大压力
2. **网络稳定性**: 建议在网络稳定的环境下运行
3. **存储空间**: 确保有足够的磁盘空间存储数据文件
4. **合法使用**: 请遵守网站的使用条款，合理使用爬虫

## 许可证

本项目仅供学习和研究使用。
