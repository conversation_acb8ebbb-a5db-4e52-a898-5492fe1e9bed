#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
丽水市天气数据合并脚本
功能：将历史天气数据和未来30天预报数据合并为统一格式
输出格式：4个字段（日期、天气状况、气温、风力风向），与历史数据格式保持一致
作者：AI Assistant
创建时间：2025-07-24
修改时间：2025-07-25
"""

import pandas as pd
import re
from datetime import datetime
import os


def parse_historical_temperature(temp_str):
    """
    解析历史数据中的温度字符串
    例如：'6℃ / 3℃' -> (最高温度: 6, 最低温度: 3)
    """
    if not temp_str or pd.isna(temp_str):
        return None, None
    
    # 匹配温度模式：数字℃ / 数字℃
    match = re.search(r'(\d+)℃\s*/\s*(\d+)℃', str(temp_str))
    if match:
        temp1, temp2 = int(match.group(1)), int(match.group(2))
        # 通常第一个是最高温度，第二个是最低温度
        return max(temp1, temp2), min(temp1, temp2)
    
    # 如果只有一个温度值
    match = re.search(r'(\d+)℃', str(temp_str))
    if match:
        temp = int(match.group(1))
        return temp, temp
    
    return None, None


def parse_forecast_temperature(temp_str):
    """
    解析预报数据中的温度字符串
    例如：'25℃' -> 25
    """
    if not temp_str or pd.isna(temp_str):
        return None
    
    match = re.search(r'(\d+)℃', str(temp_str))
    if match:
        return int(match.group(1))
    return None


def convert_forecast_weather_to_historical_format(weather_str):
    """
    将预报数据的天气状况转换为历史数据格式
    例如：'大雨转小雨' -> '大雨 / 小雨'
    """
    if not weather_str or pd.isna(weather_str):
        return ""

    weather = str(weather_str).strip()

    # 如果包含"转"，替换为" / "
    if '转' in weather:
        weather = weather.replace('转', ' / ')
    else:
        # 如果没有转换，重复相同的天气状况
        weather = f"{weather} / {weather}"

    return weather


def convert_forecast_wind_to_historical_format(wind_str):
    """
    将预报数据的风力风向转换为历史数据格式
    例如：'东北风<3级' -> '东北风 1-2级 / 东北风 1-2级'
    """
    if not wind_str or pd.isna(wind_str):
        return "无持续风向 1-2级 / 无持续风向 1-2级"

    wind = str(wind_str).strip()

    # 提取风向和风力
    # 处理类似 "东北风<3级" 或 "东风转北风<3级" 的格式
    if '<' in wind:
        # 移除 < 符号，将风力等级转换为历史格式
        wind = wind.replace('<', '')
        wind = wind.replace('3级', '1-2级')
        wind = wind.replace('4级', '3-4级')
        wind = wind.replace('5级', '4-5级')

    # 如果包含"转"，处理转换格式
    if '转' in wind:
        # 例如："东风转北风1-2级" -> "东风 1-2级 / 北风 1-2级"
        parts = wind.split('转')
        if len(parts) == 2:
            wind1 = parts[0].strip()
            wind2_full = parts[1].strip()

            # 提取第二个风向和风力
            import re
            match = re.search(r'(\S+风)\s*(\d+-\d+级)', wind2_full)
            if match:
                wind2 = match.group(1)
                level = match.group(2)
                wind = f"{wind1} {level} / {wind2} {level}"
            else:
                wind = f"{wind1} 1-2级 / {wind2_full} 1-2级"
    else:
        # 如果没有转换，重复相同的风向
        wind = f"{wind} / {wind}"

    return wind


def merge_weather_data(historical_file, forecast_file, output_file):
    """
    合并历史天气数据和预报数据

    Args:
        historical_file: 历史数据文件路径
        forecast_file: 预报数据文件路径
        output_file: 输出文件路径
    """
    print("开始合并天气数据...")

    # 读取历史数据
    print(f"读取历史数据: {historical_file}")
    historical_df = pd.read_csv(historical_file, encoding='utf-8-sig')
    print(f"历史数据记录数: {len(historical_df)}")

    # 读取预报数据
    print(f"读取预报数据: {forecast_file}")
    forecast_df = pd.read_csv(forecast_file, encoding='utf-8-sig')
    print(f"预报数据记录数: {len(forecast_df)}")

    # 处理历史数据 - 保持原有格式，只保留4个字段
    historical_processed = []
    for _, row in historical_df.iterrows():
        # 历史数据保持原有格式不变，只输出4个字段
        processed_row = {
            '日期': row['日期'],
            '天气状况': row['天气状况'],
            '气温': row['气温'],
            '风力风向': row['风力风向']
        }
        historical_processed.append(processed_row)

    # 处理预报数据 - 转换为历史数据格式
    forecast_processed = []
    for _, row in forecast_df.iterrows():
        # 转换天气状况格式
        weather = convert_forecast_weather_to_historical_format(row['天气状况'])

        # 转换温度格式：将最高温度和最低温度合并为"最高℃ / 最低℃"
        min_temp = parse_forecast_temperature(row['最低温度'])
        max_temp = parse_forecast_temperature(row['最高温度'])
        temp_str = f"{max_temp}℃ / {min_temp}℃" if min_temp and max_temp else ""

        # 转换风力风向格式
        wind = convert_forecast_wind_to_historical_format(row['风力风向'])

        # 创建历史数据格式的记录，只包含4个字段
        processed_row = {
            '日期': row['日期'],
            '天气状况': weather,
            '气温': temp_str,
            '风力风向': wind
        }
        forecast_processed.append(processed_row)

    # 合并数据
    all_data = historical_processed + forecast_processed

    # 创建合并后的DataFrame，确保列顺序正确
    merged_df = pd.DataFrame(all_data, columns=['日期', '天气状况', '气温', '风力风向'])

    # 按日期排序（可选）
    try:
        # 尝试将日期转换为datetime格式进行排序
        merged_df['日期_排序'] = pd.to_datetime(merged_df['日期'], format='%Y年%m月%d日')
        merged_df = merged_df.sort_values('日期_排序')
        merged_df = merged_df.drop('日期_排序', axis=1)
    except:
        print("警告：无法按日期排序，保持原有顺序")

    # 保存合并后的数据
    merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')

    print(f"\n合并完成！")
    print(f"总记录数: {len(merged_df)}")
    print(f"历史数据: {len(historical_processed)} 条")
    print(f"预报数据: {len(forecast_processed)} 条")
    print(f"输出文件: {output_file}")

    # 显示数据统计
    print(f"\n数据范围:")
    if len(merged_df) > 0:
        print(f"最早日期: {merged_df.iloc[0]['日期']}")
        print(f"最晚日期: {merged_df.iloc[-1]['日期']}")

    # 显示前几条和后几条数据作为预览
    print(f"\n数据预览 (前3条):")
    for i, row in merged_df.head(3).iterrows():
        print(f"{i+1}. {row['日期']} - {row['天气状况']} - {row['气温']} - {row['风力风向']}")

    print(f"\n数据预览 (后3条):")
    for i, row in merged_df.tail(3).iterrows():
        print(f"{i+1}. {row['日期']} - {row['天气状况']} - {row['气温']} - {row['风力风向']}")

    return merged_df


def main():
    """主函数"""
    print("=" * 60)
    print("丽水市天气数据合并工具")
    print("=" * 60)
    
    # 文件路径
    historical_file = "weather_data_new/丽水市_201901_202507.csv"
    forecast_file = "forecast_data/丽水市_30天天气预报_20250729_134431.csv"
    
    # 检查文件是否存在
    if not os.path.exists(historical_file):
        print(f"错误：历史数据文件不存在: {historical_file}")
        return
    
    if not os.path.exists(forecast_file):
        print(f"错误：预报数据文件不存在: {forecast_file}")
        return
    
    # 生成输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"丽水市_完整天气数据_{timestamp}.csv"
    
    try:
        # 执行合并
        merged_df = merge_weather_data(historical_file, forecast_file, output_file)

    except Exception as e:
        print(f"合并过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
