#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日期时间转换修复
"""

import os
import sys
from weather_visualization import WeatherDataVisualizer

def test_datetime_conversion():
    """测试日期时间转换修复"""
    print("=" * 60)
    print("测试日期时间转换修复")
    print("=" * 60)
    
    # 创建可视化器
    visualizer = WeatherDataVisualizer(output_dir='./test_fix_charts')
    
    # 测试日度数据加载
    daily_files = [
        '丽水市_完整天气数据_20250725_100235.csv',
        '丽水市_完整天气数据_20250724_164412.csv',
        '丽水市_完整天气数据_20250729_135351.csv'
    ]
    
    daily_file = None
    for file in daily_files:
        if os.path.exists(file):
            daily_file = file
            break
    
    if daily_file:
        print(f"1. 测试日度数据加载: {daily_file}")
        try:
            success = visualizer.load_daily_data(daily_file)
            if success:
                print("✅ 日度数据加载成功")
                print(f"   数据量: {len(visualizer.daily_data)} 条记录")
                print(f"   时间范围: {visualizer.daily_data['日期'].min()} 到 {visualizer.daily_data['日期'].max()}")
                
                # 检查年月字段
                if '年' in visualizer.daily_data.columns and '月' in visualizer.daily_data.columns:
                    print("✅ 年月字段存在")
                    print(f"   年份范围: {visualizer.daily_data['年'].min()} - {visualizer.daily_data['年'].max()}")
                    print(f"   月份范围: {visualizer.daily_data['月'].min()} - {visualizer.daily_data['月'].max()}")
                else:
                    print("❌ 年月字段缺失")
            else:
                print("❌ 日度数据加载失败")
                return False
        except Exception as e:
            print(f"❌ 日度数据加载异常: {e}")
            return False
    else:
        print("⚠️  未找到日度数据文件")
        return False
    
    # 测试月度数据加载
    monthly_files = [
        'weather_data_new/丽水市_月度统计_201901_202507.csv',
        'weather_data_new/丽水市_月度统计_201901_202508.csv'
    ]
    
    monthly_file = None
    for file in monthly_files:
        if os.path.exists(file):
            monthly_file = file
            break
    
    if monthly_file:
        print(f"\n2. 测试月度数据加载: {monthly_file}")
        try:
            success = visualizer.load_monthly_data(monthly_file)
            if success:
                print("✅ 月度数据加载成功")
                print(f"   数据量: {len(visualizer.monthly_data)} 条记录")
                print(f"   时间范围: {visualizer.monthly_data['日期'].min()} 到 {visualizer.monthly_data['日期'].max()}")
            else:
                print("❌ 月度数据加载失败")
        except Exception as e:
            print(f"❌ 月度数据加载异常: {e}")
            print("   (月度数据加载失败不影响主要功能测试)")
    else:
        print("\n⚠️  未找到月度数据文件")
    
    # 测试关键图表生成功能
    print(f"\n3. 测试关键图表生成功能")
    
    test_functions = [
        ('温度趋势图', lambda: visualizer.plot_temperature_trend('daily')),
        ('天气分布图', lambda: visualizer.plot_weather_distribution('daily')),
        ('季节性分析图', lambda: visualizer.plot_seasonal_analysis('daily')),
        ('综合分析图', lambda: visualizer.plot_comprehensive_analysis())
    ]
    
    success_count = 0
    for name, func in test_functions:
        try:
            print(f"   测试 {name}...")
            result = func()
            if result and os.path.exists(result):
                print(f"   ✅ {name} 生成成功: {os.path.basename(result)}")
                success_count += 1
            else:
                print(f"   ❌ {name} 生成失败")
        except Exception as e:
            print(f"   ❌ {name} 生成异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n4. 测试结果汇总")
    print(f"   成功生成图表: {success_count}/{len(test_functions)}")
    
    if success_count == len(test_functions):
        print("🎉 所有测试通过！日期时间转换修复成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步修复")
        return False

def test_batch_generation():
    """测试批量生成功能"""
    print(f"\n5. 测试批量生成功能")
    
    try:
        visualizer = WeatherDataVisualizer(output_dir='./test_batch_charts')
        
        # 加载数据
        daily_file = '丽水市_完整天气数据_20250725_100235.csv'
        if os.path.exists(daily_file):
            visualizer.load_daily_data(daily_file)
            
            # 批量生成
            files = visualizer.generate_all_charts()
            
            print(f"   批量生成结果: {len(files)} 个文件")
            for file in files:
                if os.path.exists(file):
                    print(f"   ✅ {os.path.basename(file)}")
                else:
                    print(f"   ❌ {os.path.basename(file)} (文件不存在)")
            
            return len(files) > 0
        else:
            print("   ⚠️  数据文件不存在，跳过批量测试")
            return True
            
    except Exception as e:
        print(f"   ❌ 批量生成异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("天气数据可视化 - 日期时间转换修复测试")
    
    # 基本功能测试
    basic_success = test_datetime_conversion()
    
    # 批量生成测试
    batch_success = test_batch_generation()
    
    print("\n" + "=" * 60)
    print("最终测试结果")
    print("=" * 60)
    
    if basic_success and batch_success:
        print("🎉 所有测试通过！")
        print("✅ 日期时间转换问题已修复")
        print("✅ 所有可视化功能正常工作")
        print("✅ 批量生成功能正常")
    else:
        print("❌ 测试失败，需要进一步调试")
        if not basic_success:
            print("   - 基本功能测试失败")
        if not batch_success:
            print("   - 批量生成测试失败")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
