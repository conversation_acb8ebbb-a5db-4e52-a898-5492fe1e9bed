#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气数据可视化测试脚本
用于测试基本的可视化功能
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import re
import os
from datetime import datetime
import warnings

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

def parse_temperature(temp_str):
    """解析温度字符串"""
    if not temp_str or pd.isna(temp_str):
        return None, None
    
    # 匹配 "数字℃ / 数字℃" 格式
    match = re.search(r'(\d+)℃\s*/\s*(\d+)℃', str(temp_str))
    if match:
        temp1, temp2 = float(match.group(1)), float(match.group(2))
        return max(temp1, temp2), min(temp1, temp2)
    
    return None, None

def test_basic_visualization():
    """测试基本可视化功能"""
    print("开始测试天气数据可视化...")
    
    # 检查数据文件
    daily_file = "丽水市_完整天气数据_20250725_100235.csv"
    if not os.path.exists(daily_file):
        print(f"错误：找不到数据文件 {daily_file}")
        return
    
    # 读取数据
    print(f"正在读取数据文件: {daily_file}")
    df = pd.read_csv(daily_file, encoding='utf-8-sig')
    print(f"成功读取 {len(df)} 条记录")
    
    # 解析日期和温度
    df['日期'] = pd.to_datetime(df['日期'], format='%Y年%m月%d日')
    temp_data = df['气温'].apply(parse_temperature)
    df['最高温度'] = [t[0] for t in temp_data]
    df['最低温度'] = [t[1] for t in temp_data]
    
    # 过滤有效数据
    df = df.dropna(subset=['最高温度', '最低温度'])
    print(f"有效温度数据: {len(df)} 条")
    
    # 创建输出目录
    output_dir = './test_charts'
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试1: 简单的温度趋势图
    print("正在生成温度趋势图...")
    plt.figure(figsize=(12, 6))
    
    # 取最近1000条数据进行绘制（避免数据过多导致显示问题）
    plot_data = df.tail(1000)
    
    plt.plot(plot_data['日期'], plot_data['最高温度'], 
             label='最高温度', color='red', alpha=0.7, linewidth=1)
    plt.plot(plot_data['日期'], plot_data['最低温度'], 
             label='最低温度', color='blue', alpha=0.7, linewidth=1)
    plt.fill_between(plot_data['日期'], plot_data['最低温度'], plot_data['最高温度'], 
                     alpha=0.2, color='gray', label='温度范围')
    
    plt.title('丽水市温度趋势图（最近1000天）', fontsize=14, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('温度 (℃)', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # 保存图片
    filepath1 = os.path.join(output_dir, '温度趋势图_测试.png')
    plt.savefig(filepath1, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"温度趋势图已保存: {filepath1}")
    
    # 测试2: 温度分布直方图
    print("正在生成温度分布图...")
    plt.figure(figsize=(10, 6))
    
    plt.hist(df['最高温度'], bins=30, alpha=0.7, color='red', label='最高温度分布')
    plt.hist(df['最低温度'], bins=30, alpha=0.7, color='blue', label='最低温度分布')
    
    plt.title('丽水市温度分布直方图', fontsize=14, fontweight='bold')
    plt.xlabel('温度 (℃)', fontsize=12)
    plt.ylabel('频次', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    # 保存图片
    filepath2 = os.path.join(output_dir, '温度分布图_测试.png')
    plt.savefig(filepath2, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"温度分布图已保存: {filepath2}")
    
    # 测试3: 月度平均温度
    print("正在生成月度平均温度图...")
    df['年月'] = df['日期'].dt.to_period('M')
    monthly_avg = df.groupby('年月').agg({
        '最高温度': 'mean',
        '最低温度': 'mean'
    }).round(1)
    
    plt.figure(figsize=(12, 6))
    
    # 转换为日期格式用于绘图
    monthly_dates = [period.to_timestamp() for period in monthly_avg.index]
    
    plt.plot(monthly_dates, monthly_avg['最高温度'], 
             marker='o', label='月平均最高温', color='red', linewidth=2)
    plt.plot(monthly_dates, monthly_avg['最低温度'], 
             marker='o', label='月平均最低温', color='blue', linewidth=2)
    
    plt.title('丽水市月度平均温度变化', fontsize=14, fontweight='bold')
    plt.xlabel('时间', fontsize=12)
    plt.ylabel('温度 (℃)', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # 保存图片
    filepath3 = os.path.join(output_dir, '月度平均温度_测试.png')
    plt.savefig(filepath3, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"月度平均温度图已保存: {filepath3}")
    
    # 生成简单的统计报告
    print("\n正在生成统计报告...")
    report_lines = [
        f"# 丽水市天气数据分析报告（测试版）",
        f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        f"",
        f"## 基本统计",
        f"- 数据记录数: {len(df)} 条",
        f"- 时间范围: {df['日期'].min().strftime('%Y-%m-%d')} 到 {df['日期'].max().strftime('%Y-%m-%d')}",
        f"- 最高温度: {df['最高温度'].max():.1f}℃",
        f"- 最低温度: {df['最低温度'].min():.1f}℃",
        f"- 平均最高温: {df['最高温度'].mean():.1f}℃",
        f"- 平均最低温: {df['最低温度'].mean():.1f}℃",
        f"",
        f"## 生成的图表",
        f"1. {os.path.basename(filepath1)}",
        f"2. {os.path.basename(filepath2)}",
        f"3. {os.path.basename(filepath3)}",
    ]
    
    report_file = os.path.join(output_dir, '分析报告_测试.txt')
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print(f"分析报告已保存: {report_file}")
    
    print("\n" + "="*50)
    print("测试完成！")
    print(f"输出目录: {output_dir}")
    print("生成的文件:")
    for i, file in enumerate([filepath1, filepath2, filepath3, report_file], 1):
        print(f"{i}. {os.path.basename(file)}")
    print("="*50)

if __name__ == "__main__":
    test_basic_visualization()
