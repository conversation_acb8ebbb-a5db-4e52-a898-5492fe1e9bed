#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气数据可视化快速开始脚本
一键生成天气数据可视化图表
"""

import os
import sys
from weather_visualization import WeatherDataVisualizer

def find_data_files():
    """自动查找数据文件"""
    daily_files = []
    monthly_files = []
    
    # 查找当前目录下的数据文件
    for file in os.listdir('.'):
        if file.endswith('.csv'):
            if '完整天气数据' in file or '天气数据' in file:
                daily_files.append(file)
            elif '月度统计' in file:
                monthly_files.append(file)
    
    # 查找weather_data_new目录下的文件
    if os.path.exists('weather_data_new'):
        for file in os.listdir('weather_data_new'):
            if file.endswith('.csv'):
                full_path = os.path.join('weather_data_new', file)
                if '月度统计' in file:
                    monthly_files.append(full_path)
                elif '天气数据' in file:
                    daily_files.append(full_path)
    
    return daily_files, monthly_files

def quick_start():
    """快速开始函数"""
    print("=" * 60)
    print("天气数据可视化工具 - 快速开始")
    print("=" * 60)
    
    # 自动查找数据文件
    print("正在查找数据文件...")
    daily_files, monthly_files = find_data_files()
    
    print(f"找到 {len(daily_files)} 个日度数据文件")
    print(f"找到 {len(monthly_files)} 个月度数据文件")
    
    if not daily_files and not monthly_files:
        print("\n错误：没有找到任何数据文件！")
        print("请确保当前目录或weather_data_new目录下有以下格式的CSV文件：")
        print("- 日度数据：包含'完整天气数据'或'天气数据'的文件名")
        print("- 月度数据：包含'月度统计'的文件名")
        return
    
    # 选择数据文件（优先选择最新的文件）
    daily_file = None
    monthly_file = None

    # 优先选择包含最新时间戳的文件
    if daily_files:
        # 查找包含20250725的文件（我们知道这是正确格式的文件）
        for file in daily_files:
            if '20250725' in file:
                daily_file = file
                break
        if not daily_file:
            daily_file = daily_files[0]

    if monthly_files:
        monthly_file = monthly_files[0]
    
    if daily_file:
        print(f"选择日度数据文件: {daily_file}")
    if monthly_file:
        print(f"选择月度数据文件: {monthly_file}")
    
    # 创建可视化分析器
    output_dir = './quick_start_charts'
    print(f"\n图表将保存到: {output_dir}")
    
    visualizer = WeatherDataVisualizer(output_dir=output_dir)
    
    # 生成所有图表
    try:
        print("\n开始生成可视化图表...")
        visualizer.create_all_visualizations(daily_file, monthly_file)
        
        print("\n" + "=" * 60)
        print("快速开始完成！")
        print("=" * 60)
        print(f"所有图表已保存到: {output_dir}")
        
        # 列出生成的文件
        if os.path.exists(output_dir):
            files = [f for f in os.listdir(output_dir) if f.endswith(('.png', '.txt'))]
            if files:
                print("生成的文件:")
                for i, file in enumerate(files, 1):
                    print(f"{i}. {file}")
        
        print("\n提示：")
        print("- PNG文件是可视化图表")
        print("- TXT文件是分析报告")
        print("- 所有文件支持中文显示")
        
    except Exception as e:
        print(f"\n生成图表时出现错误: {e}")
        print("请检查数据文件格式是否正确")
        import traceback
        traceback.print_exc()

def show_help():
    """显示帮助信息"""
    print("""
天气数据可视化工具 - 快速开始

用法:
    python quick_start.py          # 自动查找数据文件并生成图表
    python quick_start.py --help   # 显示此帮助信息

支持的数据文件:
    - 日度数据: 文件名包含'完整天气数据'或'天气数据'的CSV文件
    - 月度数据: 文件名包含'月度统计'的CSV文件

数据文件位置:
    - 当前目录下的CSV文件
    - weather_data_new/目录下的CSV文件

生成的图表类型:
    - 温度趋势图
    - 天气状况分布图  
    - 季节性分析图
    - 综合分析图
    - 数据摘要报告

输出目录: ./quick_start_charts/
""")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        show_help()
    else:
        quick_start()

if __name__ == "__main__":
    main()
