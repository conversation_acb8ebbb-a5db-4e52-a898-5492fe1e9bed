#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
丽水市未来30天天气预报爬虫
目标网站: http://www.jihetianqi.com/lishui/
功能: 爬取丽水市未来30天的天气预报数据，包括日期、天气状况、温度、风力风向等信息
作者: AI Assistant
创建时间: 2025-07-24
版本: 1.0

使用方法:
    python lishui_weather_crawler.py

输出文件:
    - CSV格式: 丽水市_30天天气预报_YYYYMMDD_HHMMSS.csv
    - JSON格式: 丽水市_30天天气预报_YYYYMMDD_HHMMSS.json
"""

import requests
import pandas as pd
import json
import time
import re
import os
from datetime import datetime
from bs4 import BeautifulSoup
from typing import List, Dict, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('weather_forecast.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class LishuiWeatherCrawler:
    """丽水市30天天气预报爬虫类"""
    
    def __init__(self, output_dir: str = './forecast_data'):
        """
        初始化爬虫
        
        Args:
            output_dir: 输出目录
        """
        self.base_url = "http://www.jihetianqi.com/lishui/"
        self.output_dir = output_dir
        self.session = requests.Session()
        
        # 设置请求头，模拟真实浏览器
        self.headers = {
            'User-Agent': ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                          'AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/91.0.4472.124 Safari/537.36'),
            'Accept': ('text/html,application/xhtml+xml,application/xml;'
                      'q=0.9,image/webp,*/*;q=0.8'),
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)
        
        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            logger.info(f"创建输出目录: {output_dir}")
    
    def fetch_weather_page(self, max_retries: int = 3) -> Optional[str]:
        """
        获取天气预报页面内容
        
        Args:
            max_retries: 最大重试次数
            
        Returns:
            页面HTML内容，失败返回None
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"正在获取丽水天气预报页面... "
                           f"(尝试 {attempt + 1}/{max_retries})")
                
                response = self.session.get(self.base_url, timeout=30)
                response.raise_for_status()
                
                # 尝试不同编码
                try:
                    content = response.content.decode('utf-8')
                except UnicodeDecodeError:
                    try:
                        content = response.content.decode('gbk')
                    except UnicodeDecodeError:
                        content = response.content.decode('utf-8', errors='ignore')
                
                logger.info("成功获取页面内容")
                return content
                
            except requests.exceptions.RequestException as e:
                logger.error(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(5 * (attempt + 1))  # 递增延迟
        
        logger.error("获取页面失败，已达到最大重试次数")
        return None
    
    def parse_weather_data(self, html_content: str) -> List[Dict[str, str]]:
        """
        解析天气数据
        
        Args:
            html_content: HTML内容
            
        Returns:
            天气数据列表
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除脚本和样式标签
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 获取纯文本内容
            text = soup.get_text()
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            
            weather_data = []
            
            # 根据观察到的模式解析数据
            # 模式：日期行 -> 天气(+1) -> 温度(+2) -> 风向(+3) -> 风力(+4)
            for i, line in enumerate(lines):
                # 查找日期行
                date_pattern = r'(\d{4}年\d{2}月\d{2}日)（星期[一二三四五六日]\)'
                date_match = re.search(date_pattern, line)
                if date_match:
                    date = date_match.group(1)
                    
                    # 初始化变量
                    weather = ""
                    temp_range = ""
                    min_temp = ""
                    max_temp = ""
                    wind_direction = ""
                    wind_level = ""
                    
                    # 查看接下来的几行
                    for j in range(1, 8):
                        if i + j < len(lines):
                            next_line = lines[i + j]
                            
                            # 尝试匹配天气状况（第1行）
                            if j == 1:
                                weather = next_line
                            
                            # 尝试匹配温度（第2行）
                            elif j == 2:
                                temp_pattern = r'^(\d+)℃\s*~\s*(\d+)℃$'
                                temp_match = re.match(temp_pattern, next_line)
                                if temp_match:
                                    temp_range = next_line
                                    min_temp = temp_match.group(1)
                                    max_temp = temp_match.group(2)
                            
                            # 尝试匹配风向（第3行）
                            elif j == 3:
                                wind_direction = next_line
                            
                            # 尝试匹配风力（第4行）
                            elif j == 4:
                                wind_pattern = r'^<?(\d+)级$'
                                wind_level_match = re.match(wind_pattern, next_line)
                                if wind_level_match:
                                    wind_level = wind_level_match.group(1)
                                    
                                    # 如果找到了完整信息，保存数据
                                    if weather and temp_range and wind_direction:
                                        wind_info = f"{wind_direction}<{wind_level}级"
                                        weather_data.append({
                                            '日期': date,
                                            '天气状况': weather,
                                            '最低温度': f"{min_temp}℃",
                                            '最高温度': f"{max_temp}℃",
                                            '温度范围': temp_range,
                                            '风力风向': wind_info,
                                            '湿度': '',
                                            '其他信息': ''
                                        })
                                    break
            
            logger.info(f"成功解析 {len(weather_data)} 条天气记录")
            return weather_data
            
        except Exception as e:
            logger.error(f"解析天气数据时出错: {e}")
            return []
    
    def save_to_csv(self, weather_data: List[Dict[str, str]], 
                    filename: str = None) -> str:
        """
        保存数据到CSV文件
        
        Args:
            weather_data: 天气数据列表
            filename: 文件名，如果为None则自动生成
            
        Returns:
            保存的文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"丽水市_30天天气预报_{timestamp}.csv"
        
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            df = pd.DataFrame(weather_data)
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            logger.info(f"成功保存数据到: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"保存CSV文件失败: {e}")
            raise
    
    def save_to_json(self, weather_data: List[Dict[str, str]], 
                     filename: str = None) -> str:
        """
        保存数据到JSON文件
        
        Args:
            weather_data: 天气数据列表
            filename: 文件名，如果为None则自动生成
            
        Returns:
            保存的文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"丽水市_30天天气预报_{timestamp}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump({
                    'city': '丽水市',
                    'crawl_time': datetime.now().isoformat(),
                    'data_count': len(weather_data),
                    'weather_data': weather_data
                }, f, ensure_ascii=False, indent=2)
            
            logger.info(f"成功保存数据到: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"保存JSON文件失败: {e}")
            raise
    
    def crawl_weather_forecast(self) -> Dict[str, any]:
        """
        执行天气预报爬取
        
        Returns:
            爬取结果字典
        """
        logger.info("开始爬取丽水市30天天气预报...")
        
        # 获取页面内容
        html_content = self.fetch_weather_page()
        if not html_content:
            return {
                'success': False,
                'message': '获取页面内容失败',
                'data': []
            }
        
        # 解析天气数据
        weather_data = self.parse_weather_data(html_content)
        if not weather_data:
            return {
                'success': False,
                'message': '解析天气数据失败',
                'data': []
            }
        
        # 保存数据
        try:
            csv_file = self.save_to_csv(weather_data)
            json_file = self.save_to_json(weather_data)
            
            return {
                'success': True,
                'message': f'成功爬取 {len(weather_data)} 条天气记录',
                'data': weather_data,
                'files': {
                    'csv': csv_file,
                    'json': json_file
                }
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'保存数据失败: {e}',
                'data': weather_data
            }


def main():
    """主函数"""
    print("=" * 60)
    print("丽水市未来30天天气预报爬虫")
    print("目标网站: http://www.jihetianqi.com/lishui/")
    print("=" * 60)
    
    # 创建爬虫实例
    crawler = LishuiWeatherCrawler()
    
    # 执行爬取
    result = crawler.crawl_weather_forecast()
    
    # 输出结果
    if result['success']:
        print(f"\n✓ {result['message']}")
        print(f"✓ 数据已保存到:")
        for file_type, filepath in result['files'].items():
            print(f"  - {file_type.upper()}: {filepath}")
        
        # 显示数据统计
        data = result['data']
        if data:
            print(f"\n数据统计:")
            print(f"- 总记录数: {len(data)}")
            print(f"- 日期范围: {data[0]['日期']} 到 {data[-1]['日期']}")
            
            # 统计天气状况
            weather_types = {}
            for item in data:
                weather = item['天气状况']
                weather_types[weather] = weather_types.get(weather, 0) + 1
            
            print(f"- 天气状况分布:")
            for weather, count in weather_types.items():
                print(f"  {weather}: {count}天")
            
            # 显示前几条数据作为预览
            print(f"\n数据预览 (前3条):")
            for i, item in enumerate(data[:3], 1):
                print(f"{i}. {item['日期']} - {item['天气状况']} - "
                      f"{item['温度范围']} - {item['风力风向']}")
    else:
        print(f"\n✗ 爬取失败: {result['message']}")
        if result['data']:
            print(f"已获取 {len(result['data'])} 条数据，但保存失败")


if __name__ == "__main__":
    main()
