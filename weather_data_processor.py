#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气数据处理器 - 将日度数据转换为月度统计数据
处理爬取的天气CSV文件，生成月度汇总统计
"""

import pandas as pd
import numpy as np
import re
import os
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class WeatherDataProcessor:
    """天气数据处理器类"""
    
    def __init__(self):
        """初始化处理器"""
        self.data = None
        self.monthly_stats = None
    
    def load_data(self, file_path: str) -> bool:
        """
        加载天气数据文件
        
        Args:
            file_path: CSV文件路径
            
        Returns:
            是否加载成功
        """
        try:
            logger.info(f"正在加载数据文件: {file_path}")
            
            # 尝试不同的编码方式
            encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
            
            for encoding in encodings:
                try:
                    self.data = pd.read_csv(file_path, encoding=encoding)
                    logger.info(f"成功使用 {encoding} 编码加载数据")
                    break
                except UnicodeDecodeError:
                    continue
            
            if self.data is None:
                logger.error("无法使用任何编码方式加载数据")
                return False
            
            logger.info(f"数据加载完成，共 {len(self.data)} 条记录")
            logger.info(f"数据列: {list(self.data.columns)}")
            
            return True
            
        except Exception as e:
            logger.error(f"加载数据时出错: {e}")
            return False
    
    def parse_date(self, date_str: str) -> tuple:
        """
        解析日期字符串，提取年月信息
        
        Args:
            date_str: 日期字符串，如 "2023年01月02日"
            
        Returns:
            (年, 月) 元组
        """
        try:
            # 使用正则表达式提取年月
            match = re.search(r'(\d{4})年(\d{1,2})月', str(date_str))
            if match:
                year = int(match.group(1))
                month = int(match.group(2))
                return year, month
            else:
                logger.warning(f"无法解析日期: {date_str}")
                return None, None
        except Exception as e:
            logger.error(f"解析日期时出错: {e}")
            return None, None
    
    def parse_temperature(self, temp_str: str) -> tuple:
        """
        解析温度字符串，提取最高温和最低温
        
        Args:
            temp_str: 温度字符串，如 "14℃ / 3℃"
            
        Returns:
            (最高温, 最低温) 元组
        """
        try:
            # 使用正则表达式提取温度数字
            temps = re.findall(r'(-?\d+)℃', str(temp_str))
            if len(temps) >= 2:
                max_temp = int(temps[0])
                min_temp = int(temps[1])
                return max_temp, min_temp
            elif len(temps) == 1:
                temp = int(temps[0])
                return temp, temp
            else:
                return None, None
        except Exception as e:
            logger.error(f"解析温度时出错: {e}")
            return None, None
    
    def parse_weather_condition(self, weather_str: str) -> tuple:
        """
        解析天气状况，提取白天和夜间天气
        
        Args:
            weather_str: 天气字符串，如 "晴 / 多云"
            
        Returns:
            (白天天气, 夜间天气) 元组
        """
        try:
            if ' / ' in str(weather_str):
                parts = str(weather_str).split(' / ')
                return parts[0].strip(), parts[1].strip()
            else:
                weather = str(weather_str).strip()
                return weather, weather
        except Exception as e:
            logger.error(f"解析天气状况时出错: {e}")
            return None, None
    
    def process_to_monthly(self) -> bool:
        """
        将日度数据处理为月度统计数据
        
        Returns:
            是否处理成功
        """
        if self.data is None:
            logger.error("请先加载数据")
            return False
        
        try:
            logger.info("开始处理月度统计数据...")
            
            # 创建处理后的数据列表
            processed_data = []
            
            for index, row in self.data.iterrows():
                # 解析日期
                year, month = self.parse_date(row['日期'])
                if year is None or month is None:
                    continue
                
                # 解析温度
                max_temp, min_temp = self.parse_temperature(row['气温'])
                
                # 解析天气状况
                day_weather, night_weather = self.parse_weather_condition(row['天气状况'])
                
                processed_data.append({
                    '年': year,
                    '月': month,
                    '年月': f"{year}-{month:02d}",
                    '日期': row['日期'],
                    '最高温': max_temp,
                    '最低温': min_temp,
                    '白天天气': day_weather,
                    '夜间天气': night_weather,
                    '风力风向': row['风力风向']
                })
            
            # 转换为DataFrame
            processed_df = pd.DataFrame(processed_data)
            
            # 按年月分组进行统计
            monthly_stats = []
            
            for year_month, group in processed_df.groupby('年月'):
                year = group['年'].iloc[0]
                month = group['月'].iloc[0]
                
                # 温度统计
                max_temps = group['最高温'].dropna()
                min_temps = group['最低温'].dropna()
                
                # 天气状况统计
                day_weather_counts = group['白天天气'].value_counts()
                night_weather_counts = group['夜间天气'].value_counts()
                
                # 计算各种统计指标
                stats = {
                    '年': year,
                    '月': month,
                    '年月': year_month,
                    '天数': len(group),
                    
                    # 温度统计
                    '月最高温': max_temps.max() if not max_temps.empty else None,
                    '月最低温': min_temps.min() if not min_temps.empty else None,
                    '平均最高温': round(max_temps.mean(), 1) if not max_temps.empty else None,
                    '平均最低温': round(min_temps.mean(), 1) if not min_temps.empty else None,
                    '平均温度': round((max_temps.mean() + min_temps.mean()) / 2, 1) if not max_temps.empty and not min_temps.empty else None,
                    
                    # 天气状况统计
                    '主要白天天气': day_weather_counts.index[0] if not day_weather_counts.empty else None,
                    '白天天气天数': day_weather_counts.iloc[0] if not day_weather_counts.empty else None,
                    '主要夜间天气': night_weather_counts.index[0] if not night_weather_counts.empty else None,
                    '夜间天气天数': night_weather_counts.iloc[0] if not night_weather_counts.empty else None,
                    
                    # 降水统计
                    '降水天数': len(group[group['白天天气'].str.contains('雨|雪', na=False) | 
                                    group['夜间天气'].str.contains('雨|雪', na=False)]),
                    '晴天天数': len(group[group['白天天气'].str.contains('晴', na=False)]),
                    '多云天数': len(group[group['白天天气'].str.contains('多云|阴', na=False)])
                }
                
                monthly_stats.append(stats)
            
            self.monthly_stats = pd.DataFrame(monthly_stats)
            logger.info(f"月度统计完成，共生成 {len(self.monthly_stats)} 个月的统计数据")
            
            return True
            
        except Exception as e:
            logger.error(f"处理月度数据时出错: {e}")
            return False
    
    def save_monthly_stats(self, output_path: str) -> bool:
        """
        保存月度统计数据
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            是否保存成功
        """
        if self.monthly_stats is None:
            logger.error("请先处理月度统计数据")
            return False
        
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 保存为CSV文件
            self.monthly_stats.to_csv(output_path, index=False, encoding='utf-8-sig')
            logger.info(f"月度统计数据已保存到: {output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"保存月度统计数据时出错: {e}")
            return False
    
    def print_summary(self):
        """打印数据摘要"""
        if self.monthly_stats is None:
            logger.error("请先处理月度统计数据")
            return
        
        logger.info("=== 月度统计数据摘要 ===")
        logger.info(f"统计期间: {self.monthly_stats['年月'].min()} 到 {self.monthly_stats['年月'].max()}")
        logger.info(f"总月数: {len(self.monthly_stats)}")
        
        # 温度摘要
        overall_max = self.monthly_stats['月最高温'].max()
        overall_min = self.monthly_stats['月最低温'].min()
        avg_temp = self.monthly_stats['平均温度'].mean()
        
        logger.info(f"历史最高温: {overall_max}℃")
        logger.info(f"历史最低温: {overall_min}℃")
        logger.info(f"平均温度: {avg_temp:.1f}℃")
        
        # 显示前几行数据
        logger.info("\n前5个月的统计数据:")
        print(self.monthly_stats.head().to_string(index=False))


def main():
    """主函数"""
    # 输入文件路径
    input_file = r"丽水市_完整天气数据_20250729_135351.csv"
    
    # 输出文件路径
    output_file = r"weather_data_new\丽水市_月度统计_201901_202508.csv"
    
    # 创建处理器实例
    processor = WeatherDataProcessor()
    
    # 处理数据
    if processor.load_data(input_file):
        if processor.process_to_monthly():
            processor.print_summary()
            processor.save_monthly_stats(output_file)
            logger.info("数据处理完成！")
        else:
            logger.error("月度数据处理失败")
    else:
        logger.error("数据加载失败")


if __name__ == "__main__":
    main()
