#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气数据可视化分析脚本
功能：对日度和月度天气数据进行多维度可视化分析
支持数据类型：
1. 日度天气数据：包含日期、天气状况、气温、风力风向
2. 月度统计数据：包含月度汇总的天气统计信息
作者：AI Assistant
创建时间：2025-07-25
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import re
from datetime import datetime
import os
import warnings
from typing import Optional, Tuple
import matplotlib.dates as mdates

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
# 尝试使用seaborn样式，如果失败则使用默认样式
try:
    plt.style.use('seaborn-v0_8')
except Exception:
    try:
        plt.style.use('seaborn')
    except Exception:
        plt.style.use('default')
warnings.filterwarnings('ignore')


class WeatherDataVisualizer:
    """天气数据可视化分析器"""
    
    def __init__(self, output_dir: str = './weather_charts'):
        """
        初始化可视化分析器
        
        Args:
            output_dir: 图表输出目录
        """
        self.output_dir = output_dir
        self.daily_data = None
        self.monthly_data = None
        self.city_name = "未知城市"
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 设置图表样式
        self.setup_plot_style()
    
    def setup_plot_style(self):
        """设置图表样式"""
        plt.rcParams['figure.figsize'] = (12, 8)
        plt.rcParams['font.size'] = 10
        plt.rcParams['axes.titlesize'] = 14
        plt.rcParams['axes.labelsize'] = 12
        plt.rcParams['xtick.labelsize'] = 10
        plt.rcParams['ytick.labelsize'] = 10
        plt.rcParams['legend.fontsize'] = 10
    
    def parse_temperature(self, temp_str: str) -> Tuple[Optional[float], Optional[float]]:
        """
        解析温度字符串
        
        Args:
            temp_str: 温度字符串，如 "29℃ / 24℃"
            
        Returns:
            (最高温度, 最低温度) 的元组
        """
        if not temp_str or pd.isna(temp_str):
            return None, None
        
        # 匹配 "数字℃ / 数字℃" 格式
        match = re.search(r'(\d+)℃\s*/\s*(\d+)℃', str(temp_str))
        if match:
            temp1, temp2 = float(match.group(1)), float(match.group(2))
            return max(temp1, temp2), min(temp1, temp2)
        
        # 匹配单个温度值
        match = re.search(r'(\d+)℃', str(temp_str))
        if match:
            temp = float(match.group(1))
            return temp, temp
        
        return None, None
    
    def parse_weather_condition(self, weather_str: str) -> Tuple[str, str]:
        """
        解析天气状况字符串
        
        Args:
            weather_str: 天气状况字符串，如 "阵雨 / 阴"
            
        Returns:
            (白天天气, 夜间天气) 的元组
        """
        if not weather_str or pd.isna(weather_str):
            return "未知", "未知"
        
        # 分割天气状况
        if ' / ' in weather_str:
            parts = weather_str.split(' / ')
            return parts[0].strip(), parts[1].strip()
        else:
            return weather_str.strip(), weather_str.strip()
    
    def load_daily_data(self, file_path: str) -> bool:
        """
        加载日度天气数据
        
        Args:
            file_path: 日度数据文件路径
            
        Returns:
            加载是否成功
        """
        try:
            print(f"正在加载日度数据: {file_path}")
            
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            
            # 验证必要字段
            required_columns = ['日期', '天气状况', '气温', '风力风向']
            if not all(col in df.columns for col in required_columns):
                print(f"错误：文件缺少必要字段。需要：{required_columns}")
                return False
            
            # 解析日期
            df['日期'] = pd.to_datetime(df['日期'], format='%Y年%m月%d日')
            
            # 解析温度
            temp_data = df['气温'].apply(self.parse_temperature)
            df['最高温度'] = [t[0] for t in temp_data]
            df['最低温度'] = [t[1] for t in temp_data]
            
            # 解析天气状况
            weather_data = df['天气状况'].apply(self.parse_weather_condition)
            df['白天天气'] = [w[0] for w in weather_data]
            df['夜间天气'] = [w[1] for w in weather_data]
            
            # 添加时间相关字段
            df['年'] = df['日期'].dt.year
            df['月'] = df['日期'].dt.month
            df['日'] = df['日期'].dt.day
            df['年月'] = df['日期'].dt.to_period('M')
            df['季节'] = df['月'].apply(self.get_season)
            
            # 过滤有效数据
            df = df.dropna(subset=['最高温度', '最低温度'])
            
            self.daily_data = df
            
            # 提取城市名称
            filename = os.path.basename(file_path)
            if '市' in filename:
                self.city_name = filename.split('市')[0] + '市'
            
            print(f"成功加载 {len(df)} 条日度数据记录")
            print(f"数据时间范围: {df['日期'].min().strftime('%Y-%m-%d')} 到 {df['日期'].max().strftime('%Y-%m-%d')}")
            
            return True
            
        except Exception as e:
            print(f"加载日度数据失败: {e}")
            return False
    
    def load_monthly_data(self, file_path: str) -> bool:
        """
        加载月度统计数据
        
        Args:
            file_path: 月度数据文件路径
            
        Returns:
            加载是否成功
        """
        try:
            print(f"正在加载月度数据: {file_path}")
            
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            
            # 验证必要字段
            required_columns = ['年', '月', '平均最高温', '平均最低温']
            if not all(col in df.columns for col in required_columns):
                print(f"错误：文件缺少必要字段。需要：{required_columns}")
                return False
            
            # 创建日期字段
            df['日期'] = pd.to_datetime(df[['年', '月']].assign(日=1))
            df['年月'] = df['日期'].dt.to_period('M')
            df['季节'] = df['月'].apply(self.get_season)
            
            self.monthly_data = df
            
            # 提取城市名称
            filename = os.path.basename(file_path)
            if '市' in filename:
                self.city_name = filename.split('市')[0] + '市'
            
            print(f"成功加载 {len(df)} 条月度数据记录")
            print(f"数据时间范围: {df['年'].min()}年{df['月'].min()}月 到 {df['年'].max()}年{df['月'].max()}月")
            
            return True
            
        except Exception as e:
            print(f"加载月度数据失败: {e}")
            return False
    
    def get_season(self, month: int) -> str:
        """
        根据月份获取季节
        
        Args:
            month: 月份 (1-12)
            
        Returns:
            季节名称
        """
        if month in [12, 1, 2]:
            return '冬季'
        elif month in [3, 4, 5]:
            return '春季'
        elif month in [6, 7, 8]:
            return '夏季'
        else:
            return '秋季'
    
    def plot_temperature_trend(self, data_type: str = 'daily') -> str:
        """
        绘制温度趋势图
        
        Args:
            data_type: 数据类型 ('daily' 或 'monthly')
            
        Returns:
            保存的图片文件路径
        """
        plt.figure(figsize=(15, 8))
        
        if data_type == 'daily' and self.daily_data is not None:
            df = self.daily_data
            x = df['日期']
            y_max = df['最高温度']
            y_min = df['最低温度']
            
            # 绘制温度趋势
            plt.plot(x, y_max, label='最高温度', color='red', alpha=0.7, linewidth=1)
            plt.plot(x, y_min, label='最低温度', color='blue', alpha=0.7, linewidth=1)
            plt.fill_between(x, y_min, y_max, alpha=0.2, color='gray', label='温度范围')
            
            # 添加移动平均线
            if len(df) > 30:
                y_max_ma = df['最高温度'].rolling(window=30, center=True).mean()
                y_min_ma = df['最低温度'].rolling(window=30, center=True).mean()
                plt.plot(x, y_max_ma, label='最高温度(30日均线)', color='darkred', linewidth=2)
                plt.plot(x, y_min_ma, label='最低温度(30日均线)', color='darkblue', linewidth=2)
            
            title = f'{self.city_name} 日度温度趋势图'
            filename = f'{self.city_name}_日度温度趋势.png'
            
        elif data_type == 'monthly' and self.monthly_data is not None:
            df = self.monthly_data
            x = df['日期']
            y_max = df['平均最高温']
            y_min = df['平均最低温']
            
            # 绘制温度趋势
            plt.plot(x, y_max, label='平均最高温', color='red', marker='o', linewidth=2)
            plt.plot(x, y_min, label='平均最低温', color='blue', marker='o', linewidth=2)
            plt.fill_between(x, y_min, y_max, alpha=0.2, color='gray', label='温度范围')
            
            title = f'{self.city_name} 月度温度趋势图'
            filename = f'{self.city_name}_月度温度趋势.png'
            
        else:
            print(f"错误：没有可用的{data_type}数据")
            return ""
        
        plt.title(title, fontsize=16, fontweight='bold')
        plt.xlabel('时间', fontsize=12)
        plt.ylabel('温度 (℃)', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 格式化x轴
        if data_type == 'daily':
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            plt.gca().xaxis.set_major_locator(mdates.MonthLocator(interval=6))
        else:
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            plt.gca().xaxis.set_major_locator(mdates.MonthLocator(interval=12))
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # 保存图片
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"温度趋势图已保存: {filepath}")
        return filepath

    def plot_weather_distribution(self, data_type: str = 'daily') -> str:
        """
        绘制天气状况分布图

        Args:
            data_type: 数据类型 ('daily' 或 'monthly')

        Returns:
            保存的图片文件路径
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        if data_type == 'daily' and self.daily_data is not None:
            df = self.daily_data

            # 白天天气分布
            day_weather = df['白天天气'].value_counts().head(10)
            ax1.pie(day_weather.values, labels=day_weather.index, autopct='%1.1f%%', startangle=90)
            ax1.set_title('白天天气状况分布', fontsize=14, fontweight='bold')

            # 夜间天气分布
            night_weather = df['夜间天气'].value_counts().head(10)
            ax2.pie(night_weather.values, labels=night_weather.index, autopct='%1.1f%%', startangle=90)
            ax2.set_title('夜间天气状况分布', fontsize=14, fontweight='bold')

            filename = f'{self.city_name}_日度天气分布.png'

        elif data_type == 'monthly' and self.monthly_data is not None:
            df = self.monthly_data

            # 主要白天天气分布
            if '主要白天天气' in df.columns:
                day_weather = df['主要白天天气'].value_counts()
                ax1.pie(day_weather.values, labels=day_weather.index, autopct='%1.1f%%', startangle=90)
                ax1.set_title('主要白天天气分布', fontsize=14, fontweight='bold')

            # 主要夜间天气分布
            if '主要夜间天气' in df.columns:
                night_weather = df['主要夜间天气'].value_counts()
                ax2.pie(night_weather.values, labels=night_weather.index, autopct='%1.1f%%', startangle=90)
                ax2.set_title('主要夜间天气分布', fontsize=14, fontweight='bold')

            filename = f'{self.city_name}_月度天气分布.png'

        else:
            print(f"错误：没有可用的{data_type}数据")
            return ""

        plt.suptitle(f'{self.city_name} 天气状况分布图', fontsize=16, fontweight='bold')
        plt.tight_layout()

        # 保存图片
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.show()

        print(f"天气分布图已保存: {filepath}")
        return filepath

    def plot_seasonal_analysis(self, data_type: str = 'daily') -> str:
        """
        绘制季节性分析图

        Args:
            data_type: 数据类型 ('daily' 或 'monthly')

        Returns:
            保存的图片文件路径
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        if data_type == 'daily' and self.daily_data is not None:
            df = self.daily_data

            # 季节温度箱线图
            seasons = ['春季', '夏季', '秋季', '冬季']
            max_temps = [df[df['季节'] == season]['最高温度'].dropna() for season in seasons]
            min_temps = [df[df['季节'] == season]['最低温度'].dropna() for season in seasons]

            ax1.boxplot(max_temps, labels=seasons)
            ax1.set_title('各季节最高温度分布', fontweight='bold')
            ax1.set_ylabel('温度 (℃)')
            ax1.grid(True, alpha=0.3)

            ax2.boxplot(min_temps, labels=seasons)
            ax2.set_title('各季节最低温度分布', fontweight='bold')
            ax2.set_ylabel('温度 (℃)')
            ax2.grid(True, alpha=0.3)

            # 月度平均温度
            monthly_avg = df.groupby('月').agg({
                '最高温度': 'mean',
                '最低温度': 'mean'
            }).round(1)

            months = range(1, 13)
            ax3.plot(months, monthly_avg['最高温度'], marker='o', label='平均最高温', color='red')
            ax3.plot(months, monthly_avg['最低温度'], marker='o', label='平均最低温', color='blue')
            ax3.set_title('月度平均温度变化', fontweight='bold')
            ax3.set_xlabel('月份')
            ax3.set_ylabel('温度 (℃)')
            ax3.set_xticks(months)
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            # 年度温度变化
            yearly_avg = df.groupby('年').agg({
                '最高温度': 'mean',
                '最低温度': 'mean'
            }).round(1)

            ax4.plot(yearly_avg.index, yearly_avg['最高温度'], marker='o', label='年均最高温', color='red')
            ax4.plot(yearly_avg.index, yearly_avg['最低温度'], marker='o', label='年均最低温', color='blue')
            ax4.set_title('年度平均温度变化', fontweight='bold')
            ax4.set_xlabel('年份')
            ax4.set_ylabel('温度 (℃)')
            ax4.legend()
            ax4.grid(True, alpha=0.3)

            filename = f'{self.city_name}_日度季节性分析.png'

        elif data_type == 'monthly' and self.monthly_data is not None:
            df = self.monthly_data

            # 季节温度箱线图
            seasons = ['春季', '夏季', '秋季', '冬季']
            max_temps = [df[df['季节'] == season]['平均最高温'].dropna() for season in seasons]
            min_temps = [df[df['季节'] == season]['平均最低温'].dropna() for season in seasons]

            ax1.boxplot(max_temps, labels=seasons)
            ax1.set_title('各季节平均最高温分布', fontweight='bold')
            ax1.set_ylabel('温度 (℃)')
            ax1.grid(True, alpha=0.3)

            ax2.boxplot(min_temps, labels=seasons)
            ax2.set_title('各季节平均最低温分布', fontweight='bold')
            ax2.set_ylabel('温度 (℃)')
            ax2.grid(True, alpha=0.3)

            # 月度平均温度
            monthly_avg = df.groupby('月').agg({
                '平均最高温': 'mean',
                '平均最低温': 'mean'
            }).round(1)

            months = range(1, 13)
            ax3.plot(months, monthly_avg['平均最高温'], marker='o', label='平均最高温', color='red')
            ax3.plot(months, monthly_avg['平均最低温'], marker='o', label='平均最低温', color='blue')
            ax3.set_title('月度平均温度变化', fontweight='bold')
            ax3.set_xlabel('月份')
            ax3.set_ylabel('温度 (℃)')
            ax3.set_xticks(months)
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            # 年度温度变化
            yearly_avg = df.groupby('年').agg({
                '平均最高温': 'mean',
                '平均最低温': 'mean'
            }).round(1)

            ax4.plot(yearly_avg.index, yearly_avg['平均最高温'], marker='o', label='年均最高温', color='red')
            ax4.plot(yearly_avg.index, yearly_avg['平均最低温'], marker='o', label='年均最低温', color='blue')
            ax4.set_title('年度平均温度变化', fontweight='bold')
            ax4.set_xlabel('年份')
            ax4.set_ylabel('温度 (℃)')
            ax4.legend()
            ax4.grid(True, alpha=0.3)

            filename = f'{self.city_name}_月度季节性分析.png'

        else:
            print(f"错误：没有可用的{data_type}数据")
            return ""

        plt.suptitle(f'{self.city_name} 季节性温度分析', fontsize=16, fontweight='bold')
        plt.tight_layout()

        # 保存图片
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.show()

        print(f"季节性分析图已保存: {filepath}")
        return filepath

    def plot_comprehensive_analysis(self) -> str:
        """
        绘制综合分析图表

        Returns:
            保存的图片文件路径
        """
        if self.daily_data is None:
            print("错误：没有可用的日度数据")
            return ""

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        df = self.daily_data

        # 1. 温度分布直方图
        ax1.hist(df['最高温度'].dropna(), bins=30, alpha=0.7, color='red', label='最高温度')
        ax1.hist(df['最低温度'].dropna(), bins=30, alpha=0.7, color='blue', label='最低温度')
        ax1.set_title('温度分布直方图', fontweight='bold')
        ax1.set_xlabel('温度 (℃)')
        ax1.set_ylabel('频次')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 温度散点图
        ax2.scatter(df['最低温度'], df['最高温度'], alpha=0.6, s=20)
        ax2.set_title('最高温度 vs 最低温度', fontweight='bold')
        ax2.set_xlabel('最低温度 (℃)')
        ax2.set_ylabel('最高温度 (℃)')

        # 添加趋势线
        if len(df) > 1:
            z = np.polyfit(df['最低温度'].dropna(), df['最高温度'].dropna(), 1)
            p = np.poly1d(z)
            ax2.plot(df['最低温度'].dropna(), p(df['最低温度'].dropna()),
                    "r--", alpha=0.8, label=f'趋势线: y={z[0]:.2f}x+{z[1]:.2f}')
            ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 月度天气天数统计
        if len(df) > 0:
            monthly_stats = df.groupby(['年', '月']).agg({
                '白天天气': lambda x: (x.str.contains('晴')).sum(),
                '夜间天气': lambda x: (x.str.contains('雨')).sum()
            }).reset_index()
            monthly_stats['日期'] = pd.to_datetime(monthly_stats[['年', '月']].assign(日=1))

            ax3.plot(monthly_stats['日期'], monthly_stats['白天天气'],
                    marker='o', label='晴天天数', color='orange')
            ax3.plot(monthly_stats['日期'], monthly_stats['夜间天气'],
                    marker='s', label='雨天天数', color='blue')
            ax3.set_title('月度天气天数变化', fontweight='bold')
            ax3.set_xlabel('时间')
            ax3.set_ylabel('天数')
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            # 格式化x轴
            ax3.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax3.xaxis.set_major_locator(mdates.MonthLocator(interval=12))
            plt.setp(ax3.xaxis.get_majorticklabels(), rotation=45)

        # 4. 温度极值统计
        yearly_extremes = df.groupby('年').agg({
            '最高温度': ['max', 'min'],
            '最低温度': ['max', 'min']
        }).round(1)

        years = yearly_extremes.index
        ax4.plot(years, yearly_extremes[('最高温度', 'max')],
                marker='o', label='年最高温', color='red', linewidth=2)
        ax4.plot(years, yearly_extremes[('最低温度', 'min')],
                marker='o', label='年最低温', color='blue', linewidth=2)
        ax4.set_title('年度极值温度变化', fontweight='bold')
        ax4.set_xlabel('年份')
        ax4.set_ylabel('温度 (℃)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.suptitle(f'{self.city_name} 综合天气分析', fontsize=16, fontweight='bold')
        plt.tight_layout()

        # 保存图片
        filename = f'{self.city_name}_综合分析.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.show()

        print(f"综合分析图已保存: {filepath}")
        return filepath

    def generate_summary_report(self) -> str:
        """
        生成数据摘要报告

        Returns:
            报告文件路径
        """
        report_lines = []
        report_lines.append(f"# {self.city_name} 天气数据分析报告")
        report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")

        if self.daily_data is not None:
            df = self.daily_data
            report_lines.append("## 日度数据统计")
            report_lines.append(f"- 数据记录数: {len(df)} 条")
            report_lines.append(f"- 时间范围: {df['日期'].min().strftime('%Y-%m-%d')} 到 {df['日期'].max().strftime('%Y-%m-%d')}")
            report_lines.append(f"- 最高温度: {df['最高温度'].max():.1f}℃ (日期: {df.loc[df['最高温度'].idxmax(), '日期'].strftime('%Y-%m-%d')})")
            report_lines.append(f"- 最低温度: {df['最低温度'].min():.1f}℃ (日期: {df.loc[df['最低温度'].idxmin(), '日期'].strftime('%Y-%m-%d')})")
            report_lines.append(f"- 平均最高温: {df['最高温度'].mean():.1f}℃")
            report_lines.append(f"- 平均最低温: {df['最低温度'].mean():.1f}℃")
            report_lines.append("")

            # 季节统计
            report_lines.append("## 季节温度统计")
            for season in ['春季', '夏季', '秋季', '冬季']:
                season_data = df[df['季节'] == season]
                if len(season_data) > 0:
                    report_lines.append(f"- {season}: 平均最高温 {season_data['最高温度'].mean():.1f}℃, 平均最低温 {season_data['最低温度'].mean():.1f}℃")
            report_lines.append("")

            # 天气状况统计
            report_lines.append("## 主要天气状况")
            top_weather = df['白天天气'].value_counts().head(5)
            for weather, count in top_weather.items():
                percentage = (count / len(df)) * 100
                report_lines.append(f"- {weather}: {count} 天 ({percentage:.1f}%)")
            report_lines.append("")

        if self.monthly_data is not None:
            df = self.monthly_data
            report_lines.append("## 月度数据统计")
            report_lines.append(f"- 数据记录数: {len(df)} 个月")
            report_lines.append(f"- 时间范围: {df['年'].min()}年{df['月'].min()}月 到 {df['年'].max()}年{df['月'].max()}月")
            report_lines.append(f"- 月平均最高温: {df['平均最高温'].mean():.1f}℃")
            report_lines.append(f"- 月平均最低温: {df['平均最低温'].mean():.1f}℃")
            report_lines.append("")

        # 保存报告
        filename = f'{self.city_name}_天气分析报告.txt'
        filepath = os.path.join(self.output_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        print(f"分析报告已保存: {filepath}")
        return filepath

    def create_all_visualizations(self, daily_file: str = None, monthly_file: str = None):
        """
        创建所有可视化图表

        Args:
            daily_file: 日度数据文件路径
            monthly_file: 月度数据文件路径
        """
        print("=" * 60)
        print(f"开始生成 {self.city_name} 天气数据可视化分析")
        print("=" * 60)

        # 加载数据
        if daily_file and os.path.exists(daily_file):
            self.load_daily_data(daily_file)

        if monthly_file and os.path.exists(monthly_file):
            self.load_monthly_data(monthly_file)

        if self.daily_data is None and self.monthly_data is None:
            print("错误：没有可用的数据文件")
            return

        generated_files = []

        # 生成各种图表
        try:
            if self.daily_data is not None:
                print("\n正在生成日度数据图表...")
                generated_files.append(self.plot_temperature_trend('daily'))
                generated_files.append(self.plot_weather_distribution('daily'))
                generated_files.append(self.plot_seasonal_analysis('daily'))
                generated_files.append(self.plot_comprehensive_analysis())

            if self.monthly_data is not None:
                print("\n正在生成月度数据图表...")
                generated_files.append(self.plot_temperature_trend('monthly'))
                generated_files.append(self.plot_weather_distribution('monthly'))
                generated_files.append(self.plot_seasonal_analysis('monthly'))

            # 生成报告
            print("\n正在生成分析报告...")
            generated_files.append(self.generate_summary_report())

        except Exception as e:
            print(f"生成图表时出现错误: {e}")
            import traceback
            traceback.print_exc()

        # 显示结果
        print("\n" + "=" * 60)
        print("可视化分析完成！")
        print("=" * 60)
        print(f"输出目录: {self.output_dir}")
        print("生成的文件:")
        for i, file in enumerate([f for f in generated_files if f], 1):
            print(f"{i}. {os.path.basename(file)}")
        print("=" * 60)


def main():
    """主函数 - 使用示例"""
    print("天气数据可视化分析工具")
    print("=" * 50)

    # 创建可视化分析器
    visualizer = WeatherDataVisualizer(output_dir='./weather_charts')

    # 示例文件路径（请根据实际情况修改）
    daily_file = "丽水市_完整天气数据_20250725_100235.csv"
    monthly_file = "weather_data_new/丽水市_月度统计_201901_202507.csv"

    # 检查文件是否存在
    if not os.path.exists(daily_file):
        print(f"警告：日度数据文件不存在: {daily_file}")
        daily_file = None

    if not os.path.exists(monthly_file):
        print(f"警告：月度数据文件不存在: {monthly_file}")
        monthly_file = None

    if daily_file is None and monthly_file is None:
        print("错误：没有找到可用的数据文件")
        print("请确保以下文件存在：")
        print("- 日度数据文件（如：丽水市_完整天气数据_*.csv）")
        print("- 月度数据文件（如：weather_data_new/*_月度统计_*.csv）")
        return

    # 生成所有可视化图表
    visualizer.create_all_visualizations(daily_file, monthly_file)


def demo_usage():
    """演示如何使用可视化工具"""
    print("\n" + "=" * 60)
    print("使用示例")
    print("=" * 60)

    # 示例1：基本使用
    print("\n1. 基本使用示例：")
    print("""
# 创建可视化分析器
visualizer = WeatherDataVisualizer(output_dir='./charts')

# 加载日度数据
visualizer.load_daily_data('丽水市_完整天气数据_20250725_100235.csv')

# 生成温度趋势图
visualizer.plot_temperature_trend('daily')

# 生成天气分布图
visualizer.plot_weather_distribution('daily')

# 生成季节性分析图
visualizer.plot_seasonal_analysis('daily')
""")

    # 示例2：月度数据分析
    print("\n2. 月度数据分析示例：")
    print("""
# 加载月度数据
visualizer.load_monthly_data('weather_data_new/丽水市_月度统计_201901_202507.csv')

# 生成月度图表
visualizer.plot_temperature_trend('monthly')
visualizer.plot_weather_distribution('monthly')
visualizer.plot_seasonal_analysis('monthly')
""")

    # 示例3：一键生成所有图表
    print("\n3. 一键生成所有图表：")
    print("""
# 一键生成所有可视化图表和报告
visualizer.create_all_visualizations(
    daily_file='丽水市_完整天气数据_20250725_100235.csv',
    monthly_file='weather_data_new/丽水市_月度统计_201901_202507.csv'
)
""")

    print("\n" + "=" * 60)
    print("支持的图表类型：")
    print("- 温度趋势图（时间序列）")
    print("- 天气状况分布图（饼图）")
    print("- 季节性温度分析（箱线图、折线图）")
    print("- 综合分析图（直方图、散点图、统计图）")
    print("- 数据摘要报告（文本文件）")
    print("=" * 60)


if __name__ == "__main__":
    # 运行主函数
    main()

    # 显示使用示例
    demo_usage()
