import pandas as pd
import numpy as np
import datetime
import requests
import time
from lxml import etree
import os
import re
from typing import List, Dict, Tuple, Optional
from multiprocessing import Pool
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 变量定义
headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,'
              'image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3',
    'Accept-Encoding': 'gzip, deflate, br',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Cache-Control': 'max-age=0',
    'Connection': 'keep-alive',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36'
}


# 默认城市配置
DEFAULT_CITIES = {
    '杭州市': 'hangzhou',
    '宁波市': 'ningbo',
    '温州市': 'wenzhou',
    '嘉兴市': 'jiaxing',
    '湖州市': 'huzhou',
    '绍兴市': 'shaoxing',
    '金华市': 'jinhua',
    '衢州市': 'quzhou',
    '舟山市': 'zhoushan',
    '台州市': 'taizhou',
    '丽水市': 'lishui'
}

class WeatherScraper:
    """天气数据爬虫类"""

    def __init__(self, output_dir: str = './weather_data'):
        """
        初始化爬虫

        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        self.session = requests.Session()
        self.session.headers.update(headers)

        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

    def generate_date_range(self, start_year: int, start_month: int,
                          end_year: int, end_month: int) -> List[str]:
        """
        生成日期范围

        Args:
            start_year: 开始年份
            start_month: 开始月份
            end_year: 结束年份
            end_month: 结束月份

        Returns:
            日期字符串列表，格式为YYYYMM
        """
        dates = []
        current_date = datetime.date(start_year, start_month, 1)
        end_date = datetime.date(end_year, end_month, 1)

        while current_date <= end_date:
            dates.append(current_date.strftime("%Y%m"))
            # 移动到下个月
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)

        return dates

    def extract_weather_data(self, html_content: str) -> List[Dict[str, str]]:
        """
        从HTML内容中提取天气数据

        Args:
            html_content: HTML内容

        Returns:
            天气数据列表
        """
        try:
            html = etree.HTML(html_content)
            weather_data = []

            # 查找包含天气数据的表格行
            # 根据网站结构，天气数据在表格中
            rows = html.xpath("//table//tr[position()>1]")  # 跳过表头

            for row in rows:
                cells = row.xpath(".//td")
                if len(cells) >= 4:  # 确保有足够的列
                    # 提取各列数据
                    date_cell = cells[0].xpath('string()').strip()
                    weather_cell = cells[1].xpath('string()').strip()
                    temp_cell = cells[2].xpath('string()').strip()
                    wind_cell = cells[3].xpath('string()').strip()

                    # 清理数据
                    date_clean = re.sub(r'\s+', '', date_cell)
                    weather_clean = re.sub(r'\s+', ' ', weather_cell).strip()
                    temp_clean = re.sub(r'\s+', ' ', temp_cell).strip()
                    wind_clean = re.sub(r'\s+', ' ', wind_cell).strip()

                    if date_clean and weather_clean:  # 确保关键数据不为空
                        weather_data.append({
                            '日期': date_clean,
                            '天气状况': weather_clean,
                            '气温': temp_clean,
                            '风力风向': wind_clean
                        })

            return weather_data

        except Exception as e:
            logger.error(f"解析HTML内容时出错: {e}")
            return []

    def fetch_month_data(self, city_en: str, year_month: str,
                        max_retries: int = 3) -> List[Dict[str, str]]:
        """
        获取指定城市和月份的天气数据

        Args:
            city_en: 城市英文名
            year_month: 年月，格式YYYYMM
            max_retries: 最大重试次数

        Returns:
            天气数据列表
        """
        url = f"https://www.tianqihoubao.com/lishi/{city_en}/month/{year_month}.html"

        for attempt in range(max_retries):
            try:
                logger.info(f"正在获取 {city_en} {year_month} 的天气数据... (尝试 {attempt + 1}/{max_retries})")

                response = self.session.get(url, timeout=30)
                response.raise_for_status()

                # 尝试不同的编码
                try:
                    content = response.content.decode('utf-8')
                except UnicodeDecodeError:
                    try:
                        content = response.content.decode('gbk')
                    except UnicodeDecodeError:
                        content = response.content.decode('utf-8', errors='ignore')

                weather_data = self.extract_weather_data(content)

                if weather_data:
                    logger.info(f"成功获取 {len(weather_data)} 条记录")
                    return weather_data
                else:
                    logger.warning(f"未能提取到数据: {url}")

            except requests.exceptions.RequestException as e:
                logger.error(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(5 * (attempt + 1))  # 递增延迟

        logger.error(f"获取数据失败，已达到最大重试次数: {url}")
        return []

    def scrape_city_data(self, city_cn: str, city_en: str,
                        date_list: List[str]) -> Optional[str]:
        """
        爬取单个城市的天气数据

        Args:
            city_cn: 城市中文名
            city_en: 城市英文名
            date_list: 日期列表

        Returns:
            保存的文件路径，失败返回None
        """
        all_data = []

        for date_str in date_list:
            weather_data = self.fetch_month_data(city_en, date_str)
            all_data.extend(weather_data)

            # 添加延迟避免请求过快
            time.sleep(1)

        if all_data:
            # 保存数据到CSV
            df = pd.DataFrame(all_data)
            filename = f"{city_cn}_{min(date_list)}_{max(date_list)}.csv"
            filepath = os.path.join(self.output_dir, filename)

            try:
                df.to_csv(filepath, index=False, encoding='utf-8-sig')
                logger.info(f"成功保存 {city_cn} 数据到 {filepath}，共 {len(all_data)} 条记录")
                return filepath
            except Exception as e:
                logger.error(f"保存文件失败: {e}")
                return None
        else:
            logger.warning(f"未获取到 {city_cn} 的任何数据")
            return None


    def scrape_multiple_cities(self, cities: Dict[str, str],
                             date_list: List[str],
                             max_workers: int = 4) -> Dict[str, Optional[str]]:
        """
        使用多进程爬取多个城市的数据

        Args:
            cities: 城市字典 {中文名: 英文名}
            date_list: 日期列表
            max_workers: 最大进程数

        Returns:
            结果字典 {城市名: 文件路径}
        """
        results = {}

        # 准备参数
        worker_args = []
        for city_cn, city_en in cities.items():
            worker_args.append((city_cn, city_en, date_list, self.output_dir))

        # 使用多进程
        with Pool(processes=max_workers) as pool:
            pool_results = pool.map(scrape_city_worker, worker_args)

            for city_cn, filepath in pool_results:
                results[city_cn] = filepath

        return results


# 全局工作函数，用于多进程
def scrape_city_worker(args):
    """
    多进程工作函数

    Args:
        args: (city_cn, city_en, date_list, output_dir)

    Returns:
        (city_cn, filepath)
    """
    city_cn, city_en, date_list, output_dir = args
    scraper = WeatherScraper(output_dir)
    filepath = scraper.scrape_city_data(city_cn, city_en, date_list)
    return city_cn, filepath


# 定义抓取函数（保持向后兼容）
def spyer_legacy(city, years, city_dict):
    """
    原始的抓取函数，保持向后兼容
    """
    city_data = []
    for year in years:
        url = f"http://www.tianqihoubao.com/lishi/{city_dict[city]}/month/{year}.html"
        print(url)
        print(f'正在抓取{city}{year}天气数据...')
        try:
            sess = requests.get(url, headers=headers)
        except:
            time.sleep(15)
            sess = requests.get(url, headers=headers)
        html = etree.HTML(sess.content.decode('gbk', 'ignore'))

        # 数据解析
        table = html.xpath("//div[@id='content']//table//tr[position()>3]")
        month = []
        if table:
            trs = table[0].xpath("//td")
            for tr in trs:
                cell = tr.xpath('string()')
                cell = ''.join(cell).replace("\r\n", '').replace(" ", '')
                month.append(cell)
            month = np.array(month).reshape(-1, 4)
            city_data.extend(month[1:])

    # 保存城市天气数据
    city_df = pd.DataFrame(city_data, columns=['日期', '天气状况', '气温', '风力风向'])
    city_df.to_csv(f'./2013-2021/{city}.csv', index=False, encoding='gbk')

    return {city: city_data}


def main():
    """
    主函数 - 配置化的天气数据爬取
    """
    # 配置参数
    cities_to_scrape = {
        # '杭州市': 'hangzhou',
        # '宁波市': 'ningbo',
        # '温州市': 'wenzhou',
        # '嘉兴市': 'jiaxing',
        # '湖州市': 'huzhou',
        # '绍兴市': 'shaoxing',
        # '金华市': 'jinhua',
        # '衢州市': 'quzhou',
        # '舟山市': 'zhoushan',
        # '台州市': 'taizhou',
        '丽水市': 'lishui'
    }

    # 配置日期范围 (示例：2023年1月到2024年12月)
    start_year, start_month = 2019, 1
    end_year, end_month = 2025, 7

    # 创建爬虫实例
    scraper = WeatherScraper(output_dir='./weather_data_new')

    # 生成日期列表
    date_list = scraper.generate_date_range(start_year, start_month,
                                           end_year, end_month)

    logger.info(f"开始爬取天气数据...")
    logger.info(f"城市: {list(cities_to_scrape.keys())}")
    logger.info(f"日期范围: {date_list[0]} 到 {date_list[-1]}")
    logger.info(f"总共 {len(date_list)} 个月")

    # 开始爬取
    results = scraper.scrape_multiple_cities(
        cities=cities_to_scrape,
        date_list=date_list,
        max_workers=4
    )

    # 输出结果
    logger.info("爬取完成！结果汇总:")
    for city, filepath in results.items():
        if filepath:
            logger.info(f"✓ {city}: {filepath}")
        else:
            logger.error(f"✗ {city}: 爬取失败")


def legacy_main():
    """
    原始的主函数，保持向后兼容
    """
    # 原始配置
    name_cn = ['舟山市']
    city_list = ['zhoushan']
    city_dict = {cn: en for cn, en in zip(name_cn, city_list)}

    years = list(pd.date_range('2013', '2022', freq='M'))
    years = [datetime.datetime.strftime(x, "%Y%m") for x in years][:-6]

    result = []
    spyders = []
    pool = Pool(processes=8)
    for city in name_cn:
        spyders.append(pool.apply_async(spyer_legacy, args=(city, years, city_dict)))
    pool.close()
    pool.join()

    # 遍历result列表，取出子进程对象，访问get()方法，获取返回值
    for i in spyders:
        result.append(i.get())
    print(result)


if __name__ == "__main__":
    # 使用新的主函数
    main()

    # 如果需要运行原始版本，取消下面的注释
    # legacy_main()