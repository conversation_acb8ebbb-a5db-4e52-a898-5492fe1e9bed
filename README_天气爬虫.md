# 丽水市30天天气预报爬虫

## 项目简介

这是一个专门用于爬取丽水市未来30天天气预报数据的Python爬虫脚本。该爬虫从几何天气网站（http://www.jihetianqi.com/lishui/）获取数据，并将结果保存为CSV和JSON格式。

## 功能特点

- ✅ 爬取丽水市未来30天的天气预报数据
- ✅ 提取完整的天气信息：日期、天气状况、最高/最低温度、风力风向
- ✅ 支持多种输出格式：CSV、JSON
- ✅ 智能错误处理和重试机制
- ✅ 详细的日志记录
- ✅ 遵守网站访问规则，包含适当的请求延迟
- ✅ 自动生成带时间戳的文件名

## 提取的数据字段

| 字段名 | 描述 | 示例 |
|--------|------|------|
| 日期 | 预报日期 | 2025年07月24日 |
| 天气状况 | 天气描述 | 多云、大雨转小雨 |
| 最低温度 | 当日最低温度 | 25℃ |
| 最高温度 | 当日最高温度 | 36℃ |
| 温度范围 | 温度区间 | 25℃ ~ 36℃ |
| 风力风向 | 风向和风力等级 | 东风转北风<3级 |
| 湿度 | 湿度信息（如果有） | 暂无 |
| 其他信息 | 其他气象数据 | 暂无 |

## 环境要求

### Python版本
- Python 3.6 或更高版本

### 依赖库
```bash
pip install requests pandas beautifulsoup4 lxml
```

或者使用requirements.txt：
```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

1. **直接运行主脚本**：
```bash
python lishui_weather_crawler.py
```

2. **在代码中使用**：
```python
from lishui_weather_crawler import LishuiWeatherCrawler

# 创建爬虫实例
crawler = LishuiWeatherCrawler(output_dir='./my_data')

# 执行爬取
result = crawler.crawl_weather_forecast()

if result['success']:
    print(f"成功爬取 {len(result['data'])} 条记录")
    print(f"CSV文件: {result['files']['csv']}")
    print(f"JSON文件: {result['files']['json']}")
else:
    print(f"爬取失败: {result['message']}")
```

### 自定义配置

```python
# 自定义输出目录
crawler = LishuiWeatherCrawler(output_dir='./weather_data')

# 获取原始页面内容
html_content = crawler.fetch_weather_page()

# 解析天气数据
weather_data = crawler.parse_weather_data(html_content)

# 保存为自定义文件名
csv_file = crawler.save_to_csv(weather_data, 'custom_weather.csv')
json_file = crawler.save_to_json(weather_data, 'custom_weather.json')
```

## 输出文件

### CSV格式示例
```csv
日期,天气状况,最低温度,最高温度,温度范围,风力风向,湿度,其他信息
2025年07月24日,多云,25℃,36℃,25℃ ~ 36℃,东风转北风<3级,,
2025年07月25日,大雨转小雨,24℃,29℃,24℃ ~ 29℃,东北风<3级,,
```

### JSON格式示例
```json
{
  "city": "丽水市",
  "crawl_time": "2025-07-24T15:44:50.251000",
  "data_count": 30,
  "weather_data": [
    {
      "日期": "2025年07月24日",
      "天气状况": "多云",
      "最低温度": "25℃",
      "最高温度": "36℃",
      "温度范围": "25℃ ~ 36℃",
      "风力风向": "东风转北风<3级",
      "湿度": "",
      "其他信息": ""
    }
  ]
}
```

## 文件结构

```
项目目录/
├── lishui_weather_crawler.py    # 主爬虫脚本
├── weather_forecast_crawler.py  # 原始开发版本
├── test_weather_crawler.py      # 测试脚本
├── simple_weather_parser.py     # 简化解析器（调试用）
├── debug_page_content.py        # 页面结构调试工具
├── README_天气爬虫.md           # 本文档
├── requirements.txt             # 依赖库列表
├── forecast_data/               # 默认输出目录
│   ├── 丽水市_30天天气预报_*.csv
│   └── 丽水市_30天天气预报_*.json
└── weather_forecast.log         # 日志文件
```

## 技术实现

### 爬虫策略
- 使用requests库发送HTTP请求
- 设置真实浏览器User-Agent避免被识别为机器人
- 实现智能重试机制处理网络异常
- 添加适当的请求延迟避免过于频繁的访问

### 数据解析
- 使用BeautifulSoup解析HTML内容
- 基于页面文本结构的模式匹配
- 正则表达式提取关键信息
- 容错处理确保数据完整性

### 数据存储
- 支持CSV格式，便于Excel等工具打开
- 支持JSON格式，便于程序处理
- 自动生成时间戳文件名避免覆盖
- UTF-8编码确保中文正确显示

## 注意事项

1. **网络连接**：确保网络连接正常，能够访问目标网站
2. **访问频率**：脚本已内置合理的访问延迟，请勿修改为过于频繁的请求
3. **数据准确性**：天气预报数据仅供参考，具体以官方气象部门发布为准
4. **网站变化**：如果目标网站结构发生变化，可能需要更新解析逻辑
5. **法律合规**：请遵守网站的robots.txt规则和使用条款

## 故障排除

### 常见问题

1. **无法获取页面内容**
   - 检查网络连接
   - 确认目标网站是否可访问
   - 查看日志文件了解具体错误

2. **解析数据失败**
   - 网站结构可能发生变化
   - 运行debug_page_content.py查看页面结构
   - 检查正则表达式是否需要更新

3. **保存文件失败**
   - 检查输出目录是否存在写入权限
   - 确认磁盘空间是否充足
   - 查看具体错误信息

### 调试工具

- `debug_page_content.py`：分析页面结构
- `simple_weather_parser.py`：简化版解析器
- `test_weather_crawler.py`：功能测试脚本

## 更新日志

### v1.0 (2025-07-24)
- 初始版本发布
- 实现基本的天气数据爬取功能
- 支持CSV和JSON输出格式
- 添加完整的错误处理和日志记录

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站使用条款。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue在项目仓库
- 发送邮件至项目维护者

---

**免责声明**：本工具仅用于技术学习和研究目的，使用者应当遵守相关法律法规和网站使用条款。作者不对使用本工具产生的任何后果承担责任。
